FROM eclipse-temurin:21-jre-alpine

# Cài đặt ffmpeg và các dependencies cần thiết
RUN apk add --no-cache ffmpeg fontconfig freetype ttf-dejavu

WORKDIR /app
COPY ./adtrueserver.jar app.jar
COPY ./adtrue_ads.json adtrue_ads.json

# Tạo thư mục lưu video và logs
RUN mkdir -p /vistar_ads_video && chmod 777 /vistar_ads_video
RUN mkdir -p /app/logs && chmod 777 /app/logs

# Kiểm tra phiên bản ffmpeg
RUN ffmpeg -version

# Cấu hình JVM để tối ưu cho Virtual Threads
ENV JAVA_TOOL_OPTIONS="-XX:+UseZGC -XX:+ZGenerational -Djava.awt.headless=true -Djdk.httpclient.keepalive.timeout=5 -Djdk.httpclient.keepalive.timeout.h2=5 -D-Djdk.httpclient.connectionPoolSize=1000"

EXPOSE 8080
ENTRYPOINT ["java", "-jar", "app.jar"]