#!/bin/bash

# Th<PERSON> mục lưu backup
BACKUP_DIR="/backup"
DATE=$(date +%Y%m%d_%H%M%S)

# T<PERSON><PERSON> thư mục backup nếu chưa tồn tại
mkdir -p $BACKUP_DIR

# Backup database
docker exec deploy_mariadb_1 sh -c 'exec mysqldump -uroot -p"$MARIADB_ROOT_PASSWORD" adtrue' > $BACKUP_DIR/adtrue_$DATE.sql

# Nén file backup
gzip $BACKUP_DIR/adtrue_$DATE.sql

# Xóa các backup cũ hơn 7 ngày
find $BACKUP_DIR -name "adtrue_*.sql.gz" -type f -mtime +7 -delete

# Backup video files (tùy chọn)
# tar -czf $BACKUP_DIR/videos_$DATE.tar.gz /path/to/vistar_ads_video

echo "Backup completed: $BACKUP_DIR/adtrue_$DATE.sql.gz"