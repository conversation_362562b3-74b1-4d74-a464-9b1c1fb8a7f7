user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    tcp_nodelay     on;

    keepalive_timeout  65;

    gzip  on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # Server configuration (merged from default.conf)
    server {
        listen 80;
        server_name _ *************;
        
        error_log /var/log/nginx/server_error.log warn;
        access_log /var/log/nginx/server_access.log main;
        

        # Serve video files directly
        location /videos/ {
            alias /vistar_ads_video/;
            mp4;
            mp4_buffer_size 1m;
            mp4_max_buffer_size 5m;
            add_header Cache-Control "public, max-age=3600";
            add_header Access-Control-Allow-Origin "*";
        }

        # Health check
        location /health {
            return 200 'OK';
            add_header Content-Type text/plain;
        }
    }
}