services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    restart: always
    ports:
      - "8080:8080"
      - "5005:5005"  # Expose debug port
    depends_on:
      - redis
      - kafka
      - postgres
    environment:
      - SPRING_DATA_REDIS_HOST=redis
      - SPRING_DATA_REDIS_PORT=6379
      - SPRING_KAFKA_SERVER=kafka:9092
      - SPRING_DATASOURCE_URL=**************************************
      - SPRING_DATASOURCE_USERNAME=adtrue
      - SPRING_DATASOURCE_PASSWORD=${DB_PASSWORD}
      - VISTAR_API_KEY=${VISTAR_API_KEY}
      - VISTAR_NETWORKID=${VISTAR_NETWORKID}
      - JAVA_TOOL_OPTIONS=-XX:+UseZGC -XX:+ZGenerational -Djava.awt.headless=true -Djdk.httpclient.keepalive.timeout=5 -Djdk.httpclient.keepalive.timeout.h2=5 -Djdk.httpclient.connectionPoolSize=1000 -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005
      - TZ=Asia/Ho_Chi_Minh
    volumes:
      - vistar_ads_video:/vistar_ads_video
      - app-logs:/app/logs

  postgres:
    image: timescale/timescaledb:latest-pg16
    restart: always
    environment:
      - TZ=Asia/Ho_Chi_Minh
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_USER=adtrue
      - POSTGRES_DB=adtrue
    volumes:
      - postgres-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    command: -c 'max_connections=200' -c 'shared_buffers=2GB'

  redis:
    image: redis:7.2-alpine
    restart: always
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes

  kafka:
    image: confluentinc/confluent-local:7.5.0
    restart: always
    environment:
      # Timezone
      - TZ=Asia/Ho_Chi_Minh
      # Kraft mode configuration
      - KAFKA_NODE_ID=1
      - KAFKA_PROCESS_ROLES=broker,controller
      - KAFKA_CONTROLLER_QUORUM_VOTERS=1@kafka:29093
      - KAFKA_LISTENERS=PLAINTEXT://0.0.0.0:9092,CONTROLLER://0.0.0.0:29093
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092
      - KAFKA_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT
      - KAFKA_CONTROLLER_LISTENER_NAMES=CONTROLLER
      - KAFKA_INTER_BROKER_LISTENER_NAME=PLAINTEXT

      # Retention policy - keep data for 3 months
      - KAFKA_LOG_RETENTION_HOURS=2160
      - KAFKA_LOG_RETENTION_BYTES=107374182400 # 100GB
      - KAFKA_LOG_SEGMENT_BYTES=1073741824
      - KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS=300000

      # Other configurations
      - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
      - KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR=1
      - KAFKA_TRANSACTION_STATE_LOG_MIN_ISR=1
      - KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS=0
      - KAFKA_AUTO_CREATE_TOPICS_ENABLE=true
      - CLUSTER_ID=MkU3OEVBNTcwNTJENDM2Qk
    volumes:
      - kafka-data:/var/lib/kafka/data
    ports:
      - "9092:9092"

  metabase:
    image: metabase/metabase:latest
    ports:
      - "3000:3000"
    environment:
      - MB_DB_TYPE=postgres
      - MB_DB_DBNAME=adtrue
      - MB_DB_PORT=5432
      - MB_DB_USER=adtrue
      - MB_DB_PASS=${DB_PASSWORD}
      - MB_DB_HOST=postgres
      # Default admin account setup
      - MB_ADMIN_EMAIL=<EMAIL>
      - MB_ADMIN_FIRST_NAME=Admin
      - MB_ADMIN_LAST_NAME=AdTrue
      - MB_ADMIN_PASSWORD=${METABASE_PASSWORD}
      # Skip initial setup
      - MB_SETUP_TOKEN=
    depends_on:
      - postgres
    restart: always

  prometheus:
    image: prom/prometheus:latest
    restart: always
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    depends_on:
      - app
      - redis-exporter
      - postgres-exporter
      - kafka-exporter
    dns:
      - 127.0.0.11  # Docker DNS resolver

  grafana:
    image: grafana/grafana:latest
    restart: always
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
    depends_on:
      - prometheus

  redis-exporter:
    image: oliver006/redis_exporter:latest
    restart: always
    environment:
      - REDIS_ADDR=redis:6379
    ports:
      - "9121:9121"
    depends_on:
      - redis

  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    restart: always
    environment:
      - DATA_SOURCE_NAME=postgresql://adtrue:${DB_PASSWORD}@postgres:5432/adtrue?sslmode=disable
    ports:
      - "9187:9187"
    depends_on:
      - postgres

  kafka-exporter:
    image: danielqsj/kafka-exporter:latest
    restart: always
    command:
      - '--kafka.server=kafka:9092'
    ports:
      - "9308:9308"
    depends_on:
      - kafka

volumes:
  redis-data:
  postgres-data:
  kafka-data:
  vistar_ads_video:
  app-logs:
  prometheus-data:
  grafana-data:

networks:
  default:
    name: adtrue-network
