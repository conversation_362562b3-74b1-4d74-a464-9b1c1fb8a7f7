#!/bin/bash

# Cấu hình
KAFKA_CONTAINER="adtrue-kafka-1"  # Tên container Kafka, có thể cần điều chỉnh
TOPIC_NAME="video_logging"        # Tên topic, điều chỉnh nếu cần
LOGS_DIR="/home/<USER>/adtrue"       # Th<PERSON> mục lưu log
OUTPUT_FILE="$LOGS_DIR/vast_logs_$(date +%Y%m%d_%H%M%S).txt"  # Tên file output với timestamp

# Tạo thư mục logs nếu chưa tồn tại
mkdir -p "$LOGS_DIR"

echo "Bắt đầu xuất log từ Kafka topic '$TOPIC_NAME' vào file '$OUTPUT_FILE'..."
echo "Nhấn Ctrl+C để dừng."

# Kiểm tra xem container Kafka có tồn tại không
if ! docker ps | grep -q $KAFKA_CONTAINER; then
    echo "Lỗi: Container Kafka '$KAFKA_CONTAINER' không tồn tại hoặc không chạy."
    echo "Danh sách các container đang chạy:"
    docker ps
    exit 1
fi

# Kiểm tra topic có tồn tại không
if ! docker exec $KAFKA_CONTAINER kafka-topics --bootstrap-server localhost:9092 --list | grep -q $TOPIC_NAME; then
    echo "Cảnh báo: Topic '$TOPIC_NAME' có thể không tồn tại."
    echo "Danh sách các topic hiện có:"
    docker exec $KAFKA_CONTAINER kafka-topics --bootstrap-server localhost:9092 --list
    
    read -p "Bạn có muốn tiếp tục không? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Sử dụng kafka-console-consumer để đọc tất cả tin nhắn từ đầu
docker exec -it $KAFKA_CONTAINER kafka-console-consumer \
  --bootstrap-server localhost:9092 \
  --topic $TOPIC_NAME \
  --from-beginning \
  --timeout-ms 30000 > "$OUTPUT_FILE"

# Kiểm tra kết quả
if [ -s "$OUTPUT_FILE" ]; then
    echo "Đã xuất log vào file $OUTPUT_FILE"
    echo "Tổng số log: $(wc -l < "$OUTPUT_FILE")"
    echo "Bạn có thể xem log bằng lệnh: less $OUTPUT_FILE"
else
    echo "Không có log nào được xuất ra hoặc có lỗi xảy ra."
    echo "Kiểm tra xem topic '$TOPIC_NAME' có chứa dữ liệu không."
fi

