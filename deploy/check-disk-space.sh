#!/bin/bash

# Cấu hình Telegram
TELEGRAM_BOT_TOKEN="YOUR_BOT_TOKEN_HERE"
TELEGRAM_CHAT_ID="YOUR_CHAT_ID_HERE"

# Ngưỡng cảnh báo (%)
THRESHOLD=10

# Danh sách partition cần kiểm tra (có thể là mount point hoặc device name)
# Để trống array để kiểm tra tất cả partition
PARTITIONS_TO_CHECK=(
    "/"
    "/home"
    "/var"
    "/data"
    # "/dev/sda1"
    # "/dev/nvme0n1p1"
)

# Hàm gửi message đến Telegram
send_telegram_message() {
    local message="$1"
    curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
        -d chat_id="${TELEGRAM_CHAT_ID}" \
        -d text="${message}" \
        -d parse_mode="HTML" > /dev/null
}

# Hàm kiểm tra xem partition có trong danh sách không
should_check_partition() {
    local partition="$1"
    local mount_point="$2"
    
    # Nếu array rỗng, kiểm tra tất cả
    if [ ${#PARTITIONS_TO_CHECK[@]} -eq 0 ]; then
        return 0
    fi
    
    # Kiểm tra theo mount point hoặc device name
    for check_item in "${PARTITIONS_TO_CHECK[@]}"; do
        if [[ "$mount_point" == "$check_item" ]] || [[ "$partition" == "$check_item" ]]; then
            return 0
        fi
    done
    
    return 1
}

# Kiểm tra dung lượng ổ cứng
check_disk_space() {
    # Lấy thông tin disk usage, loại bỏ header và filesystem tạm
    df -h | grep -vE '^Filesystem|tmpfs|cdrom|udev|overlay' | awk '{print $5 " " $1 " " $6}' | while read output; do
        # Tách thông tin
        usage=$(echo $output | awk '{print $1}' | sed 's/%//g')
        partition=$(echo $output | awk '{print $2}')
        mount_point=$(echo $output | awk '{print $3}')
        
        # Kiểm tra xem có cần kiểm tra partition này không
        if ! should_check_partition "$partition" "$mount_point"; then
            continue
        fi
        
        # Tính phần trăm còn trống
        free_space=$((100 - usage))
        
        echo "Checking: $partition ($mount_point) - Free: ${free_space}%"
        
        # Kiểm tra nếu dung lượng trống < ngưỡng
        if [ $free_space -lt $THRESHOLD ]; then
            hostname=$(hostname)
            current_time=$(date '+%Y-%m-%d %H:%M:%S')
            
            message="🚨 <b>CẢNH BÁO DUNG LƯỢNG Ổ CỨNG</b>

📍 <b>Server:</b> ${hostname}
💾 <b>Partition:</b> ${partition}
📂 <b>Mount Point:</b> ${mount_point}
📊 <b>Đã sử dụng:</b> ${usage}%
💿 <b>Còn trống:</b> ${free_space}%
⏰ <b>Thời gian:</b> ${current_time}

⚠️ Dung lượng trống dưới ${THRESHOLD}%!"

            echo "WARNING: ${partition} (${mount_point}) has only ${free_space}% free space"
            send_telegram_message "$message"
        fi
    done
}

# Hiển thị cấu hình hiện tại
echo "=== Disk Space Monitor ==="
echo "Threshold: ${THRESHOLD}%"
if [ ${#PARTITIONS_TO_CHECK[@]} -eq 0 ]; then
    echo "Monitoring: ALL partitions"
else
    echo "Monitoring partitions:"
    printf '  %s\n' "${PARTITIONS_TO_CHECK[@]}"
fi
echo "=========================="

# Chạy kiểm tra
check_disk_space