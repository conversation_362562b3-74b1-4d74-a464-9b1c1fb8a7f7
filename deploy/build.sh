#!/bin/bash

# <PERSON><PERSON><PERSON> bị thư mục cho volumes
#!/bin/bash

# Đ<PERSON><PERSON> DATA_PATH từ file .env
DATA_PATH=$(grep DATA_PATH .env | cut -d '=' -f2)

# Tạ<PERSON> các thư mục cần thiết
mkdir -p ${DATA_PATH}/redis-data
mkdir -p ${DATA_PATH}/mariadb-data
mkdir -p ${DATA_PATH}/kafka-data
mkdir -p ${DATA_PATH}/vistar_ads_video
mkdir -p ${DATA_PATH}/app-logs

# Thiết lập quyền
chmod -R 777 ${DATA_PATH}/vistar_ads_video
chmod -R 777 ${DATA_PATH}/app-logs

echo "Đã tạo các thư mục volume tại ${DATA_PATH}"

# Khởi động các container
docker compose -f docker-compose.prod.yml up -d --build app
