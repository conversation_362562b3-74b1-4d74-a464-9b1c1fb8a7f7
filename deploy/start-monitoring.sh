#!/bin/bash

# T<PERSON><PERSON> c<PERSON><PERSON> thư mục cần thiết
mkdir -p telegraf

# Kiểm tra xem file cấu hình Telegraf đã tồn tại chưa
if [ ! -f "telegraf/telegraf.conf" ]; then
  echo "Tạo file cấu hình Telegraf..."
  cp telegraf.conf telegraf/telegraf.conf
fi

# Khởi động các container
echo "Khởi động hệ thống monitoring..."
docker compose -f docker-compose.prod.yml up -d telegraf metabase

echo "Hệ thống monitoring đã được khởi động!"
echo "Truy cập Metabase tại: http://localhost:3000"
echo "Lần đầu tiên truy cập, bạn cần thiết lập tài khoản admin"
echo "Metabase sẽ tự động kết nối đến TimescaleDB để truy vấn dữ liệu"
