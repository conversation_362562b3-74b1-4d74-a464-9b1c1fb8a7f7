services:
  postgres:
    image: timescale/timescaledb:latest-pg16
    restart: always
    environment:
      - TZ=Asia/Ho_<PERSON>_Minh
      - POSTGRES_PASSWORD=${DEV_DB_PASSWORD:-adtrue@123}
      - POSTGRES_USER=${DEV_DB_USER:-adtrue}
      - POSTGRES_DB=adtrue
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./postgres/postgresql.conf:/etc/postgresql/postgresql.conf
    ports:
      - "5432:5432"
    command: -c 'max_connections=200' -c 'shared_buffers=2GB' -c 'listen_addresses=*'

  mariadb:
    image: mariadb:latest
    restart: always
    environment:
      - TZ=Asia/Ho_Chi_Minh
      - MYSQL_ROOT_PASSWORD=${DEV_DB_PASSWORD:-adtrue@123}
      - MYSQL_USER=${DEV_DB_USER:-adtrue}
      - MYSQL_PASSWORD=${DEV_DB_PASSWORD:-adtrue@123}
      - MYSQL_DATABASE=adtrue
    volumes:
      - mariadb-data:/var/lib/mysql
      - ./mariadb/my.cnf:/etc/mysql/conf.d/my.cnf
    ports:
      - "3306:3306"

  redis:
    image: redis:7.2-alpine
    restart: always
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes

  kafka:
    image: confluentinc/confluent-local:7.5.0
    restart: always
    environment:
      - TZ=Asia/Ho_Chi_Minh
      - KAFKA_NODE_ID=1
      - KAFKA_PROCESS_ROLES=broker,controller
      - KAFKA_CONTROLLER_QUORUM_VOTERS=1@kafka:29093
      - KAFKA_LISTENERS=PLAINTEXT://0.0.0.0:9092,CONTROLLER://0.0.0.0:29093
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092
      - KAFKA_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT
      - KAFKA_CONTROLLER_LISTENER_NAMES=CONTROLLER
      - KAFKA_INTER_BROKER_LISTENER_NAME=PLAINTEXT
      - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
      - KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR=1
      - KAFKA_TRANSACTION_STATE_LOG_MIN_ISR=1
      - KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS=0
      - KAFKA_AUTO_CREATE_TOPICS_ENABLE=true
      - CLUSTER_ID=MkU3OEVBNTcwNTJENDM2Qk
    volumes:
      - kafka-data:/var/lib/kafka/data
    ports:
      - "9092:9092"

  metabase:
    image: metabase/metabase:latest
    ports:
      - "3000:3000"
    environment:
      - MB_DB_TYPE=postgres
      - MB_DB_DBNAME=adtrue
      - MB_DB_PORT=5432
      - MB_DB_USER=${DEV_DB_USER:-adtrue}
      - MB_DB_PASS=${DEV_DB_PASSWORD:-adtrue@123}
      - MB_DB_HOST=postgres
      - MB_ADMIN_EMAIL=<EMAIL>
      - MB_ADMIN_FIRST_NAME=Admin
      - MB_ADMIN_LAST_NAME=AdTrue
      - MB_ADMIN_PASSWORD=${METABASE_PASSWORD:-adtrue@123}
      - MB_SETUP_TOKEN=
    depends_on:
      - postgres
    restart: always

  prometheus:
    image: prom/prometheus:latest
    restart: always
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    depends_on:
      - redis-exporter
      - postgres-exporter

  grafana:
    image: grafana/grafana:latest
    restart: always
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-adtrue@123}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
    depends_on:
      - prometheus

  redis-exporter:
    image: oliver006/redis_exporter:latest
    restart: always
    environment:
      - REDIS_ADDR=redis:6379
    ports:
      - "9121:9121"
    depends_on:
      - redis

  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    restart: always
    environment:
      - DATA_SOURCE_NAME=postgresql://${DEV_DB_USER:-adtrue}:${DEV_DB_PASSWORD:-adtrue@123}@postgres:5432/adtrue?sslmode=disable
    ports:
      - "9187:9187"
    depends_on:
      - postgres

volumes:
  redis-data:
  postgres-data:
  mariadb-data:
  kafka-data:
  prometheus-data:
  grafana-data:

networks:
  default:
    name: adtrue-dev-network