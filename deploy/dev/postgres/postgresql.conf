# PostgreSQL configuration file
listen_addresses = '*'          # Listen on all interfaces
max_connections = 200           # Maximum connections
shared_buffers = 2GB            # Shared buffer size

# Network settings for remote access
tcp_keepalives_idle = 60
tcp_keepalives_interval = 10
tcp_keepalives_count = 10

# Allow remote connections (this will be in pg_hba.conf but <PERSON><PERSON> will handle it)
# host    all    all    0.0.0.0/0    md5