-- Create database if not exists
CREATE DATABASE IF NOT EXISTS adtrue;
USE adtrue;

-- Create cm_report table for CM SSP reporting
CREATE TABLE IF NOT EXISTS cm_report (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    device_id VARCHAR(255) NOT NULL,
    banner_id VARCHAR(255) NOT NULL,
    campaign_id VARCHAR(255) NOT NULL,
    channel_id INT NOT NULL DEFAULT 0,
    vendor_id INT NOT NULL DEFAULT 0,
    region_id INT NOT NULL DEFAULT 0,
    city_id INT NOT NULL DEFAULT 0,
    district_id INT NOT NULL DEFAULT 0,
    ward_id INT NOT NULL DEFAULT 0,
    store_id INT NOT NULL DEFAULT 0,
    multiplier INT NOT NULL DEFAULT 1,
    event_date DATETIME NOT NULL,
    total_spot INT DEFAULT 0,
    total_vast_sent INT DEFAULT 0,
    total_start INT DEFAULT 0,
    total_first_quartile INT DEFAULT 0,
    total_midpoint INT DEFAULT 0,
    total_third_quartile INT DEFAULT 0,
    total_complete INT DEFAULT 0,
    time DATETIME NOT NULL,
    INDEX idx_cm_report_device (device_id),
    INDEX idx_cm_report_campaign (campaign_id),
    INDEX idx_cm_report_date (event_date),
    INDEX idx_cm_report_time (time)
) ENGINE=InnoDB;

-- Create vast_report table for VAST reporting
CREATE TABLE IF NOT EXISTS vast_report (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    device_id VARCHAR(255) NOT NULL,
    ssp_name VARCHAR(50) NOT NULL,
    campaign_id VARCHAR(255) NOT NULL,
    event_date DATETIME NOT NULL,
    channel_id INT NOT NULL,
    vendor_id INT NOT NULL,
    region_id INT NOT NULL,
    city_id INT NOT NULL,
    district_id INT NOT NULL,
    ward_id INT NOT NULL,
    store_id INT NOT NULL,
    multiplier INT NOT NULL,
    total_vast_sent INT DEFAULT 0,
    total_start INT DEFAULT 0,
    total_first_quartile INT DEFAULT 0,
    total_midpoint INT DEFAULT 0,
    total_third_quartile INT DEFAULT 0,
    total_complete INT DEFAULT 0,
    total_payout INT DEFAULT 0,
    total_payout_error INT DEFAULT 0,
    time DATETIME NOT NULL,
    INDEX idx_vast_report_device (device_id),
    INDEX idx_vast_report_campaign (campaign_id),
    INDEX idx_vast_report_date (event_date),
    INDEX idx_vast_report_time (time),
    INDEX idx_vast_report_location (city_id, district_id, ward_id),
    UNIQUE INDEX idx_vast_report_dimensions (
        device_id, 
        ssp_name, 
        campaign_id, 
        event_date, 
        channel_id, 
        vendor_id, 
        region_id, 
        city_id, 
        district_id, 
        ward_id, 
        store_id, 
        multiplier
    )
) ENGINE=InnoDB;

-- Create user with appropriate permissions and allow remote access
CREATE USER IF NOT EXISTS 'adtrue'@'%' IDENTIFIED BY 'adtrue@123';
GRANT ALL PRIVILEGES ON adtrue.* TO 'adtrue'@'%';
FLUSH PRIVILEGES;