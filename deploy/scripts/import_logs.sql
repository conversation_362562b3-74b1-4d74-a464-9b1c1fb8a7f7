CREATE TABLE IF NOT EXISTS log_analysis (
    id BIGSERIAL PRIMARY KEY,
    timestamp VARCHAR(50),
    device_id VARCHAR(255),
    ad_id VARCHAR(255),
    url TEXT,
    status_code INT,
    event_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COPY log_analysis (timestamp, device_id, ad_id, url, status_code, event_type) 
FROM '/scripts/extracted_logs.csv' 
WITH (FORMAT csv, HEADER true);