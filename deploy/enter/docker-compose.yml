version: '3.8'

services:
  jellyfin:
    image: jellyfin/jellyfin:latest
    restart: always
    ports:
      - "8096:8096"
    environment:
      - TZ=Asia/Ho_Chi_Minh
      - PUID=3000
      - PGID=3000
    volumes:
      - jellyfin-config:/config
      - /data:/data
    networks:
      - entertainment-network

  qbittorrent:
    image: linuxserver/qbittorrent:latest
    restart: always
    ports:
      - "8090:8080"  # WebUI port
      - "6881:6881"
      - "6881:6881/udp"
    environment:
      - TZ=Asia/Ho_Chi_Minh
      - PUID=3000
      - PGID=3000
      - WEBUI_PORT=8080
    volumes:
      - qbittorrent-config:/config
      - /data:/data
    networks:
      - entertainment-network

  samba:
    image: dperson/samba:latest
    restart: always
    ports:
      - "139:139"
      - "445:445"
    environment:
      - TZ=Asia/Ho_Chi_Minh
    volumes:
      - /data:/data
    command: >-
      -u "nas;${MEDIA_PASSWORD}" 
      -s "data;/data;yes;yes;yes;all;nas;nas" 
      -p
      -g "force user = nas" 
      -g "force group = nas"
      -g "create mask = 0777"
      -g "directory mask = 0777"
    networks:
      - entertainment-network

volumes:
  jellyfin-config:
  qbittorrent-config:

networks:
  entertainment-network:
    name: entertainment-network