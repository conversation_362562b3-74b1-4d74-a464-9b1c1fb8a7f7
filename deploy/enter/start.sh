#!/bin/bash

# Load environment variables
if [ -f .env ]; then
  export $(grep -v '^#' .env | xargs)
  echo "Loaded environment variables from .env file"
fi

# Create directories for volumes if they don't exist
DATA_PATH=${DATA_PATH:-/data}
mkdir -p ${DATA_PATH}/nas_data
mkdir -p ${DATA_PATH}/jellyfin-config
mkdir -p ${DATA_PATH}/qbittorrent-config

# Set permissions - ensure very permissive for troubleshooting
sudo chmod -R 777 ${DATA_PATH}/nas_data
sudo chown -R 1000:1000 ${DATA_PATH}/nas_data

echo "Created volume directories at ${DATA_PATH}"
echo "Set permissions on media directory"

# Start the entertainment services
docker compose down
docker compose up -d

echo "Entertainment services started!"
echo "Jellyfin is available at: http://localhost:8096"
echo "qBittorrent WebUI is available at: http://localhost:8090"
echo "Samba share is available at: \\\\SERVER_IP\\media"
echo "Samba user: nas (password from .env file)"
