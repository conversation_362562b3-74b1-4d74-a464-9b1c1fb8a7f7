#!/bin/bash

# Th<PERSON> mục lưu backup
BACKUP_DIR="/backup/logs"
DATE=$(date +%Y%m%d_%H%M%S)

# Tạo thư mục backup nếu chưa tồn tại
mkdir -p $BACKUP_DIR

# Tạo container tạm thời để backup logs
docker run --rm -v deploy_app-logs:/logs -v $BACKUP_DIR:/backup alpine sh -c "tar -czf /backup/app-logs-$DATE.tar.gz -C /logs ."

# Xóa các backup cũ hơn 30 ngày
find $BACKUP_DIR -name "app-logs-*.tar.gz" -type f -mtime +30 -delete

echo "Logs backup completed: $BACKUP_DIR/app-logs-$DATE.tar.gz"