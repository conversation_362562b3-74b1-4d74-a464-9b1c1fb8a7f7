# Maven
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties

# IDE files
### IntelliJ IDEA ###
.idea/
.idea/modules.xml
.idea/jarRepositories.xml
.idea/compiler.xml
.idea/libraries/
*.iws
*.iml
*.ipr

### Eclipse ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

# OS files
### Mac OS ###
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini

# Application specific
application-local.properties
application-dev.properties
application-prod.properties
application.properties
*.log
logs/
*.json
vistar_ads_video/

# Docker
docker-compose.override.yml
/script-tests/node_modules/
