const fs = require('node:fs');
const path = require('node:path');
const readline = require('node:readline');

// Th<PERSON> mụ<PERSON> ch<PERSON>a c<PERSON>c file log CSV
const LOG_DIR = '../log';
// Th<PERSON> mục đầu ra
const OUTPUT_DIR = './processed_logs';

// Đ<PERSON><PERSON> bảo thư mục đầu ra tồn tại
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

// Map để lưu trữ streams cho từng deviceId
const deviceStreams = new Map();

// Hàm kiểm tra thời gian có trong khoảng 8:00:00 đến 22:10:00
function isTimeInRange(timestamp) {
  try {
    // Tìm phần thời gian (HH:MM:SS)
    const timePart = timestamp.split(":");

    const hours = parseInt(timePart[0], 10);
    const minutes = parseInt(timePart[1], 10);

    // <PERSON>y<PERSON>n đổi thời gian thành phút để so sánh dễ hơn
    const timeInMinutes = hours * 60 + minutes;
    const startTime = 8 * 60; // 8:00:00 = 480 phút
    const endTime = 22 * 60 + 10; // 22:10:00 = 1330 phút

    return timeInMinutes >= startTime && timeInMinutes <= endTime;
  } catch (error) {
    console.warn(`Invalid timestamp format: ${timestamp}`);
    return false;
  }
}

// Hàm xử lý từng file CSV
async function processFile(filePath) {
  const fileName = path.basename(filePath);
  console.log(`Processing file: ${fileName}`);

  const fileStream = fs.createReadStream(filePath);
  const rl = readline.createInterface({
    input: fileStream,
    crlfDelay: Infinity
  });

  let isHeader = true;
  let totalLines = 0;
  let filteredLines = 0;

  for await (const line of rl) {
    // Bỏ qua header
    if (isHeader) {
      isHeader = false;
      continue;
    }

    totalLines++;

    const parts = line.split(',');
    if (parts.length < 7) continue;

    const timestamp = parts[0];
    const deviceId = parts[1];
    const eventType = parts[2];
    const adId = parts[3];
    let eventTimestamp = parts[4];
    const serverTimestamp = parts[5];
    if (eventType === '0')
      eventTimestamp = serverTimestamp;

    if (!deviceId) continue;

    // Kiểm tra thời gian có trong khoảng 8:00:00 đến 22:10:00
    if (!isTimeInRange(timestamp)) {
      continue;
    }

    filteredLines++;
    const streamKey = `${deviceId}_${fileName}`;
    
    // Lấy hoặc tạo stream cho deviceId
    if (!deviceStreams.has(streamKey)) {
      const outDir = path.join(OUTPUT_DIR, fileName);
      if (!fs.existsSync(outDir)) {
        fs.mkdirSync(outDir, { recursive: true });
      }
      const outputPath = path.join(outDir, streamKey);
      const stream = fs.createWriteStream(outputPath);
      // Viết header cho file mới
      stream.write('timestamp,deviceId,eventType,adId,eventTimestamp\n');
      deviceStreams.set(streamKey, stream);
    }
    
    // Ghi dữ liệu vào file tương ứng
    const stream = deviceStreams.get(streamKey);
    stream.write(`${timestamp},${deviceId},${eventType},${adId},${eventTimestamp}\n`);
  }

  console.log(`File ${fileName}: Processed ${filteredLines}/${totalLines} lines (filtered by time 8:00-22:10)`);
}

// Hàm chính để xử lý tất cả các file
async function processAllFiles() {
  try {
    // Đọc tất cả các file trong thư mục log
    const files = fs.readdirSync(LOG_DIR);
    
    // Lọc các file CSV
    const csvFiles = files.filter(file => file.endsWith('.csv'));
    
    // Xử lý từng file
    for (const file of csvFiles) {
      await processFile(path.join(LOG_DIR, file));
    }
    
    // Đóng tất cả các streams
    for (const [deviceId, stream] of deviceStreams.entries()) {
      stream.end();
      console.log(`Completed processing for device: ${deviceId}`);
    }
    
    console.log('All files processed successfully!');
  } catch (error) {
    console.error('Error processing files:', error);
  }
}

// Chạy chương trình
processAllFiles();