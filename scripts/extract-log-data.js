const fs = require('node:fs');
const path = require('node:path');
const readline = require('node:readline');

// Th<PERSON> mục chứa các file log JSON
const LOG_DIR = '../log';
// File đầu ra SQL
const OUTPUT_FILE = './extracted_logs.sql';

// Tạo bảng SQL
const createTableSQL = `
CREATE TABLE IF NOT EXISTS log_analysis (
    id BIGSERIAL PRIMARY KEY,
    timestamp VARCHAR(50),
    device_id VARCHAR(255),
    ad_id VARCHAR(255),
    url TEXT,
    status_code INT,
    event_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

`;

// Hàm escape string cho SQL
function escapeSQLString(str) {
    if (!str) return 'NULL';
    // Escape single quotes và backslashes
    return `'${str.replace(/\\/g, '\\\\').replace(/'/g, "''")}'`;
}

// Hàm clean JSON string
function cleanJsonString(jsonStr) {
    // Loại bỏ các control characters không hợp lệ
    let cleaned = jsonStr.replace(/[\x00-\x1F\x7F]/g, '');
    
    // Fix các lỗi JSON phổ biến
    // Thêm dấu phẩy thiếu trước các property
    cleaned = cleaned.replace(/}(\s*)"([^"]+)":/g, '}, "$2":');
    
    // Fix trailing comma
    cleaned = cleaned.replace(/,(\s*[}\]])/g, '$1');
    
    // Fix double quotes trong string values
    cleaned = cleaned.replace(/"([^"]*)"([^"]*)"([^"]*)":/g, '"$1\\"$2\\"$3":');
    
    return cleaned;
}

// Hàm parse JSON an toàn
function safeJsonParse(jsonStr) {
    try {
        return JSON.parse(jsonStr);
    } catch (e) {
        // Thử clean và parse lại
        try {
            const cleaned = cleanJsonString(jsonStr);
            return JSON.parse(cleaned);
        } catch (e2) {
            // Nếu vẫn lỗi, thử extract bằng regex
            return extractFieldsWithRegex(jsonStr);
        }
    }
}

// Hàm extract fields bằng regex khi JSON parse fail
function extractFieldsWithRegex(line) {
    const fields = {};
    
    // Extract timestamp
    const timestampMatch = line.match(/"timestamp":\s*"([^"]+)"/);
    if (timestampMatch) fields.timestamp = timestampMatch[1];
    
    // Extract deviceId
    const deviceIdMatch = line.match(/"deviceId":\s*"([^"]+)"/);
    if (deviceIdMatch) fields.deviceId = deviceIdMatch[1];
    
    // Extract adId
    const adIdMatch = line.match(/"adId":\s*"([^"]+)"/);
    if (adIdMatch) fields.adId = adIdMatch[1];
    
    // Extract url
    const urlMatch = line.match(/"url":\s*"([^"]+)"/);
    if (urlMatch) fields.url = urlMatch[1];
    
    // Extract statusCode
    const statusCodeMatch = line.match(/"statusCode":\s*"?([^",}]+)"?/);
    if (statusCodeMatch) fields.statusCode = statusCodeMatch[1];
    
    // Extract eventType
    const eventTypeMatch = line.match(/"eventType":\s*"([^"]+)"/);
    if (eventTypeMatch) fields.eventType = eventTypeMatch[1];
    
    return fields;
}

// Hàm xử lý từng file JSON log
async function processFile(filePath, writeStream) {
    const fileName = path.basename(filePath);
    console.log(`Processing file: ${fileName}`);

    const fileStream = fs.createReadStream(filePath);
    const rl = readline.createInterface({
        input: fileStream,
        crlfDelay: Infinity
    });

    let processedLines = 0;
    let errorLines = 0;

    for await (const line of rl) {
        if (!line.trim()) continue;

        try {
            // Parse JSON an toàn
            const logData = safeJsonParse(line);

            // Trích xuất các trường cần thiết
            const timestamp = logData.timestamp || '';
            const deviceId = logData.deviceId || '';
            const adId = logData.adId || '';
            const url = logData.url || '';
            const statusCode = logData.statusCode || '';
            const eventType = logData.eventType || '';

            // Tạo câu lệnh INSERT
            const insertSQL = `INSERT INTO log_analysis (timestamp, device_id, ad_id, url, status_code, event_type) VALUES (${escapeSQLString(timestamp)}, ${escapeSQLString(deviceId)}, ${escapeSQLString(adId)}, ${escapeSQLString(url)}, ${statusCode || 'NULL'}, ${escapeSQLString(eventType)});\n`;
            
            writeStream.write(insertSQL);
            processedLines++;

        } catch (error) {
            errorLines++;
            console.warn(`Failed to parse line in ${fileName} error ${error.message}: ${line.substring(0, 30)}...`);
        }
    }

    console.log(`File ${fileName}: Processed ${processedLines} lines, ${errorLines} errors`);
}

// Hàm chính
async function extractLogData() {
    try {
        // Tạo file SQL output
        const writeStream = fs.createWriteStream(OUTPUT_FILE);
        
        // Viết CREATE TABLE
        writeStream.write(createTableSQL);
        writeStream.write('\n-- Data insertion\n');

        // Đọc tất cả các file trong thư mục log
        const files = fs.readdirSync(LOG_DIR);
        
        // Lọc các file log (json, log)
        const logFiles = files.filter(file => 
            file.endsWith('.json') || 
            file.includes('tracking')
        );
        
        console.log(`Found ${logFiles.length} log files`);

        // Xử lý từng file
        for (const file of logFiles) {
            await processFile(path.join(LOG_DIR, file), writeStream);
        }
        
        writeStream.end();
        console.log(`SQL file generated: ${OUTPUT_FILE}`);
        
    } catch (error) {
        console.error('Error extracting log data:', error);
    }
}

// Chạy chương trình
extractLogData();