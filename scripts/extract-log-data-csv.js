const fs = require('node:fs');
const path = require('node:path');
const readline = require('node:readline');

const LOG_DIR = '../log';
const OUTPUT_CSV = './extracted_logs.csv';
const OUTPUT_SQL = './import_logs.sql';

// Tạo SQL script để import CSV
const importSQL = `
CREATE TABLE IF NOT EXISTS log_analysis (
    id BIGSERIAL PRIMARY KEY,
    timestamp VARCHAR(50),
    device_id VARCHAR(255),
    ad_id VARCHAR(255),
    url TEXT,
    status_code INT,
    event_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COPY log_analysis (timestamp, device_id, ad_id, url, status_code, event_type) 
FROM '${path.resolve(OUTPUT_CSV)}' 
WITH (FORMAT csv, HEADER true, DELIMITER ',', QUOTE '"', ESCAPE '"');
`;

async function extractToCSV() {
    const writeStream = fs.createWriteStream(OUTPUT_CSV);
    
    // Write CSV header
    writeStream.write('timestamp,device_id,ad_id,url,status_code,event_type\n');
    
    const files = fs.readdirSync(LOG_DIR);
    const logFiles = files.filter(file => 
        file.endsWith('.json') || file.includes('tracking')
    );
    
    for (const file of logFiles) {
        await processFileToCSV(path.join(LOG_DIR, file), writeStream);
    }
    
    writeStream.end();
    
    // Tạo SQL import script
    fs.writeFileSync(OUTPUT_SQL, importSQL);
    
    console.log(`CSV file: ${OUTPUT_CSV}`);
    console.log(`Import script: ${OUTPUT_SQL}`);
}

// Hàm escape CSV field
function escapeCsvField(str) {
    if (!str) return '';
    // Escape double quotes và wrap trong quotes nếu có comma, newline hoặc quotes
    if (str.includes(',') || str.includes('\n') || str.includes('"')) {
        return `"${str.replace(/"/g, '""')}"`;
    }
    return str;
}

// Hàm parse JSON an toàn
function safeJsonParse(jsonStr) {
    try {
        return JSON.parse(jsonStr);
    } catch (e) {
        // Extract fields bằng regex khi JSON parse fail
        return extractFieldsWithRegex(jsonStr);
    }
}

// Hàm extract fields bằng regex
function extractFieldsWithRegex(line) {
    const fields = {};
    
    const timestampMatch = line.match(/"timestamp":\s*"([^"]+)"/);
    if (timestampMatch) fields.timestamp = timestampMatch[1];
    
    const deviceIdMatch = line.match(/"deviceId":\s*"([^"]+)"/);
    if (deviceIdMatch) fields.deviceId = deviceIdMatch[1];
    
    const adIdMatch = line.match(/"adId":\s*"([^"]+)"/);
    if (adIdMatch) fields.adId = adIdMatch[1];
    
    const urlMatch = line.match(/"url":\s*"([^"]+)"/);
    if (urlMatch) fields.url = urlMatch[1];
    
    const statusCodeMatch = line.match(/"statusCode":\s*"?([^",}]+)"?/);
    if (statusCodeMatch) fields.statusCode = statusCodeMatch[1];
    
    const eventTypeMatch = line.match(/"eventType":\s*"([^"]+)"/);
    if (eventTypeMatch) fields.eventType = eventTypeMatch[1];
    
    return fields;
}

async function processFileToCSV(filePath, writeStream) {
    const fileName = path.basename(filePath);
    console.log(`Processing file: ${fileName}`);

    const fileStream = fs.createReadStream(filePath);
    const rl = readline.createInterface({
        input: fileStream,
        crlfDelay: Infinity
    });

    let processedLines = 0;
    let errorLines = 0;

    for await (const line of rl) {
        if (!line.trim()) continue;

        try {
            const logData = safeJsonParse(line);

            const timestamp = logData.timestamp || '';
            const deviceId = logData.deviceId || '';
            const adId = logData.adId || '';
            const url = logData.url || '';
            const statusCode = logData.statusCode || '';
            const eventType = logData.eventType || '';

            // Tạo CSV row
            const csvRow = `${escapeCsvField(timestamp)},${escapeCsvField(deviceId)},${escapeCsvField(adId)},${escapeCsvField(url)},${statusCode},${escapeCsvField(eventType)}\n`;
            
            writeStream.write(csvRow);
            processedLines++;

        } catch (error) {
            errorLines++;
            console.warn(`Failed to parse line in ${fileName}: ${line.substring(0, 50)}...`);
        }
    }

    console.log(`File ${fileName}: Processed ${processedLines} lines, ${errorLines} errors`);
}

// Chạy chương trình
extractToCSV();