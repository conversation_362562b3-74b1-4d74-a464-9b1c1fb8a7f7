#!/bin/bash

# <PERSON><PERSON><PERSON> để migrate dữ liệu từ MariaDB sang PostgreSQL
# Lưu ý: Cần cài đặt pgloader để thực hiện migration

echo "<PERSON><PERSON><PERSON> đầu migrate dữ liệu từ MariaDB sang PostgreSQL..."

# Tạo file cấu hình pgloader
cat > migration.load << EOF
LOAD DATABASE
     FROM      mysql://adtrue:${DB_PASSWORD}@mariadb:3306/adtrue
     INTO      postgresql://adtrue:${DB_PASSWORD}@postgres:5432/adtrue

WITH data only, truncate

SET work_mem to '128MB', maintenance_work_mem to '512 MB'

CAST type datetime to timestamp,
     type date to date,
     type time to time,
     type tinyint to smallint,
     type smallint to smallint,
     type mediumint to integer,
     type int to integer,
     type bigint to bigint,
     type float to float,
     type double to double precision,
     type decimal to numeric,
     type char to character,
     type varchar to character varying,
     type tinytext to text,
     type text to text,
     type mediumtext to text,
     type longtext to text

EXCLUDING TABLE NAMES MATCHING 'flyway_schema_history'

;
EOF

# Thực hiện migration
pgloader migration.load

echo "Ho<PERSON>n thành migration dữ liệu!"