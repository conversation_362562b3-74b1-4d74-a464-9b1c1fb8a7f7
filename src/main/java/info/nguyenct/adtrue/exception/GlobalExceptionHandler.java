package info.nguyenct.adtrue.exception;

import info.nguyenct.adtrue.ssp.cm.CMSsp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.HashMap;
import java.util.Map;

@ControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * Xử lý lỗi tài nguyên tĩnh không tồn tại
     */
    @ExceptionHandler(NoResourceFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseEntity<Object> handleNoResourceFoundException(NoResourceFoundException ex) {
        // <PERSON><PERSON><PERSON> tra nếu request là cho OpenMetrics
        if (isOpenMetricsRequest()) {
            // Trả về lỗi dạng OpenMetrics
            String openMetricsError = "# ERROR\n" +
                    "error_message \"Resource not found\"\n";
            return ResponseEntity
                    .status(HttpStatus.NOT_FOUND)
                    .contentType(MediaType.parseMediaType("application/openmetrics-text;version=1.0.0;charset=utf-8"))
                    .body(openMetricsError);
        }

        // Ghi log ở mức DEBUG thay vì ERROR vì đây là lỗi phổ biến
        logger.debug("Resource not found: {}", ex.getMessage());
        
        // Trả về response 404
        Map<String, Object> response = new HashMap<>();
        response.put("error", "Resource not found");
        response.put("message", ex.getMessage());
        
        return new ResponseEntity<>(response, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<Object> handleGenericException(Exception ex) {
        // Kiểm tra nếu request là cho OpenMetrics
        if (isOpenMetricsRequest()) {
            // Ghi log chi tiết về lỗi OpenMetrics
            logOpenMetricsError(ex);
            
            // Trả về lỗi dạng OpenMetrics
            String openMetricsError = "# ERROR\n" +
                    "error_message \"" + ex.getMessage().replace("\"", "\\\"") + "\"\n";
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.parseMediaType("application/openmetrics-text;version=1.0.0;charset=utf-8"))
                    .body(openMetricsError);
        }

        // Xử lý bình thường cho các request khác
        Map<String, Object> response = new HashMap<>();
        response.put("error", "Internal server error");
        response.put("message", ex.getMessage());

        logger.error("Internal server error", ex);

        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    
    private void logOpenMetricsError(Exception ex) {
        try {
            org.springframework.web.context.request.RequestAttributes requestAttributes = 
                org.springframework.web.context.request.RequestContextHolder.getRequestAttributes();
            
            if (requestAttributes instanceof org.springframework.web.context.request.ServletRequestAttributes) {
                org.springframework.web.context.request.ServletRequestAttributes servletRequestAttributes = 
                    (org.springframework.web.context.request.ServletRequestAttributes) requestAttributes;
                
                jakarta.servlet.http.HttpServletRequest request = servletRequestAttributes.getRequest();
                
                // Ghi log chi tiết
                logger.error("OpenMetrics request error: {}", ex.getMessage(), ex);
                logger.error("Request details: URI={}, Method={}, ContentType={}, Accept={}",
                    request.getRequestURI(),
                    request.getMethod(),
                    request.getContentType(),
                    request.getHeader("Accept"));
                
                // Ghi log các header
                java.util.Enumeration<String> headerNames = request.getHeaderNames();
                while (headerNames.hasMoreElements()) {
                    String headerName = headerNames.nextElement();
                    logger.error("Header: {}={}", headerName, request.getHeader(headerName));
                }
            }
        } catch (Exception e) {
            logger.error("Error logging OpenMetrics request details: {}", e.getMessage(), e);
        }
    }
    
    private boolean isOpenMetricsRequest() {
        try {
            // Lấy request hiện tại từ RequestContextHolder
            org.springframework.web.context.request.RequestAttributes requestAttributes = 
                org.springframework.web.context.request.RequestContextHolder.getRequestAttributes();
            
            if (requestAttributes instanceof org.springframework.web.context.request.ServletRequestAttributes) {
                org.springframework.web.context.request.ServletRequestAttributes servletRequestAttributes = 
                    (org.springframework.web.context.request.ServletRequestAttributes) requestAttributes;
                
                jakarta.servlet.http.HttpServletRequest request = servletRequestAttributes.getRequest();
                
                // Kiểm tra Accept header
                String acceptHeader = request.getHeader("Accept");
                boolean isOpenMetrics = acceptHeader != null && 
                       (acceptHeader.contains("application/openmetrics-text") || 
                        acceptHeader.contains("text/plain"));
                
                // Ghi log kết quả kiểm tra
                if (isOpenMetrics) {
                    logger.info("Detected OpenMetrics request: URI={}, Accept={}", 
                        request.getRequestURI(), acceptHeader);
                }
                
                return isOpenMetrics;
            }
        } catch (Exception e) {
            logger.warn("Error checking if request is OpenMetrics: {}", e.getMessage());
        }
        
        return false;
    }
}