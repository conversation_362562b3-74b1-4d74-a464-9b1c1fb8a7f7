package info.nguyenct.adtrue.restclient;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import info.nguyenct.adtrue.util.Util;
import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.MDC;

import java.net.URI;
import java.net.http.HttpRequest;
import java.time.Duration;

public class HttpPostJsonClient<T> extends HttpRestClient<T> {

    private final ObjectMapper objectMapper;

    public HttpPostJsonClient(String serviceName, MeterRegistry meterRegistry, ObjectMapper objectMapper, Class<T> clazz) {
        super(serviceName, meterRegistry, new Util.MyJsonDeserialize<>(objectMapper, clazz));
        this.objectMapper = objectMapper;
    }


    public HttpResult<T> sendPost(String url, Object input) {
        try {
            String requestBody = objectMapper.writeValueAsString(input);
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(encodeUrl(url)))
                    .header("Content-Type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .timeout(Duration.ofSeconds(5))
                    .build();

            return processHttpRequest(url, request, requestBody);
        } catch (JsonProcessingException e) {
            logger.error("Error serializing request body: {}", e.getMessage(), e);
            return new HttpResult<>(-1, null, null, 0, e);
        }
    }

}
