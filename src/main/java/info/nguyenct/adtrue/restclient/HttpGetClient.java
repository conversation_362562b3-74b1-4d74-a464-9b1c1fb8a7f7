package info.nguyenct.adtrue.restclient;

import io.micrometer.core.instrument.MeterRegistry;
import java.net.URI;
import java.net.http.HttpRequest;
import java.util.function.Function;

public class HttpGetClient<T> extends HttpRestClient<T> {

    public HttpGetClient(String serviceName, MeterRegistry meterRegistry, Function<String, T> deserializer) {
        super(serviceName, meterRegistry, deserializer);
    }

    public HttpResult<T> sendGet(String url) {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(encodeUrl(url)))
                .GET()
                .build();
        return processHttpRequest(url, request, "");
    }
}
