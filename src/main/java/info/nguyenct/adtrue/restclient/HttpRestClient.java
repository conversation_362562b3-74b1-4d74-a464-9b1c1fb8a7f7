package info.nguyenct.adtrue.restclient;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

public abstract class HttpRestClient<T> {
    protected final String serviceName;
    protected final Logger logger;
    protected final Counter requestCounter;
    protected final Timer successTimer, failureTimer;
    protected final HttpClient httpClient;
    protected final Function<String, T> deserializer;

    protected HttpRestClient(String serviceName, MeterRegistry meterRegistry, Function<String, T> deserializer) {
        this.serviceName = serviceName;
        this.logger = LoggerFactory.getLogger(this.serviceName);

        this.requestCounter = Counter.builder(serviceName + ".requests")
                .description("Number of requests of " + serviceName)
                .register(meterRegistry);
        this.successTimer = Timer.builder(serviceName + ".requests.success.duration")
                .description("Time taken for " + serviceName + " API success requests")
                .distributionStatisticExpiry(Duration.ofMinutes(5))
                .publishPercentileHistogram(true)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register(meterRegistry);
        this.failureTimer = Timer.builder(serviceName + ".request.failure.duration")
                .description("Time taken for " + serviceName + " API failure requests")
                .distributionStatisticExpiry(Duration.ofMinutes(5))
                .publishPercentileHistogram(true)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register(meterRegistry);
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(5))
                .executor(Executors.newVirtualThreadPerTaskExecutor())
                .version(HttpClient.Version.HTTP_2)
                .followRedirects(HttpClient.Redirect.NORMAL)
                // Add HTTP/2 specific settings
                .sslContext(getSSLContext())
                .build();
        this.deserializer = deserializer;
    }

    private SSLContext getSSLContext() {
        try {
            return SSLContext.getDefault();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Failed to create SSL context", e);
        }
    }

    protected  HttpResult<T> processHttpRequest(String url, HttpRequest request, String requestBody) {
        logger.atDebug()
                .addKeyValue("service", serviceName)
                .addKeyValue("url", url)
                .addKeyValue("requestBody", requestBody)
                .log("start calling");
        long start = System.currentTimeMillis();
        try {
            requestCounter.increment();
            HttpResponse<String> response = this.httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            int statusCode = response.statusCode();
            String body = response.body();
            long duration = System.currentTimeMillis() - start;
            if (statusCode == 200) {
                T out = deserializer.apply(body);
                successTimer.record(duration, TimeUnit.MILLISECONDS);
                return addToLog(url, requestBody, new HttpResult<>(statusCode, out, body, duration, null));
            } else {
                failureTimer.record(duration, TimeUnit.MILLISECONDS);
                return addToLog(url, requestBody, new HttpResult<>(statusCode, null, body, duration, null));
            }
        } catch (Exception ex) {
            long duration = System.currentTimeMillis() - start;
            failureTimer.record(duration, TimeUnit.MILLISECONDS);
            return addToLog(url, requestBody, new HttpResult<>(-1, null, null, duration, ex));
        }
    }

    protected <T> HttpResult<T> addToLog(String url, String requestBody, HttpResult<T> result) {
        if (result.isSuccess()) {
            logger.atInfo()
                    .addKeyValue("service", serviceName)
                    .addKeyValue("url", url)
                    .addKeyValue("statusCode", result.statusCode())
                    .addKeyValue("value", result.value())
                    .addKeyValue("requestBody", requestBody)
                    .addKeyValue("body", result.body().trim())
                    .addKeyValue("duration", result.duration())
                    .log("Successful   ");
        } else if (result.exception() != null) {
            logger.atError()
                    .addKeyValue("service", serviceName)
                    .addKeyValue("url", url)
                    .addKeyValue("requestBody", requestBody)
                    .addKeyValue("duration", result.duration())
                    .log("Failed with exception {}", result.exception().getClass().getName());
        } else {
            logger.atError()
                    .addKeyValue("service", serviceName)
                    .addKeyValue("url", url)
                    .addKeyValue("statusCode", result.statusCode())
                    .addKeyValue("requestBody", requestBody)
                    .addKeyValue("body", result.body().trim())
                    .addKeyValue("duration", result.duration())
                    .log("Failed with status {}", result.statusCode());
        }
        return result;
    }

    // Encode URL to handle special characters
    protected String encodeUrl(String url) {
        try {
            // Split URL into base and query string
            int queryIndex = url.indexOf('?');
            if (queryIndex == -1) {
                return url; // No query string
            }

            String baseUrl = url.substring(0, queryIndex);
            String queryString = url.substring(queryIndex + 1);

            // Split query parameters
            String[] params = queryString.split("&");
            StringBuilder encodedQuery = new StringBuilder();

            for (int i = 0; i < params.length; i++) {
                String[] keyValue = params[i].split("=", 2);
                String key = keyValue[0];

                if (i > 0) {
                    encodedQuery.append("&");
                }

                encodedQuery.append(key).append("=");

                if (keyValue.length > 1) {
                    // Encode parameter value
                    String value = keyValue[1];
                    encodedQuery.append(java.net.URLEncoder.encode(value, "UTF-8"));
                }
            }

            return baseUrl + "?" + encodedQuery;
        } catch (Exception e) {
            logger.warn("Error encoding URL: {}", url, e);
            return url; // Return original URL if error occurs
        }
    }

    public HttpClientMetrics getMetrics() {
        return new HttpClientMetrics(
                (int) Math.round(requestCounter.count()),
                (int) successTimer.count(),
                (int) failureTimer.count(),
                (int) Math.round(successTimer.percentile(0.95, TimeUnit.MILLISECONDS)),
                (int) Math.round(failureTimer.percentile(0.95, TimeUnit.MILLISECONDS))
        );
    }   
    
}