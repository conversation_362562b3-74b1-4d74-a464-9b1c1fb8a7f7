package info.nguyenct.adtrue.controller;

import info.nguyenct.adtrue.model.AdtrueAd;
import info.nguyenct.adtrue.model.SspDevice;
import info.nguyenct.adtrue.workflow.AdsGettingWorkflow;
import info.nguyenct.adtrue.service.KafkaService;
import info.nguyenct.adtrue.service.SspDeviceManager;
import info.nguyenct.adtrue.util.Util;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/vast")
@Tag(name = "VAST API", description = "API để phục vụ quảng cáo video theo chuẩn VAST")
public class VastController {
    private static final Logger logger = LoggerFactory.getLogger(VastController.class);
    // Khởi tạo logger cho CSV
    private static final Logger vastEventLogger = LoggerFactory.getLogger("vast_events");

    Logger oldEventsLogger = LoggerFactory.getLogger("vast_old_events");
    
    private final AdsGettingWorkflow adsGettingWorkflow;
    
    @Value("${server.port:8080}")
    private String serverPort;

    private final KafkaService kafkaService;
    private final SspDeviceManager sspDeviceManager;

    public VastController(AdsGettingWorkflow adsGettingWorkflow, KafkaService kafkaService, 
                         SspDeviceManager sspDeviceManager) {
        this.adsGettingWorkflow = adsGettingWorkflow;
        this.kafkaService = kafkaService;
        this.sspDeviceManager = sspDeviceManager;
    }
    
    @GetMapping(value = "/getxml", produces = MediaType.APPLICATION_XML_VALUE)
    @Operation(summary = "Lấy VAST XML cho thiết bị", description = "Trả về VAST XML chứa thông tin quảng cáo video cho thiết bị")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "VAST XML được tạo thành công", 
                content = @Content(mediaType = MediaType.APPLICATION_XML_VALUE))
    })
    public ResponseEntity<String> getVastXml(
            @Parameter(description = "Device ID của thiết bị yêu cầu quảng cáo") 
            @RequestParam(value = "d", required = false) String deviceId,
            @RequestParam(value = "mac", required = false) String mac,
            @Parameter(description = "Timestamp của thời điểm yêu cầu, nếu không có sẽ lấy thời gian hiện tại") 
            @RequestParam(value = "t", required = false, defaultValue = "0") Long requestedTimestamp,
            @Parameter(description = "SSP cụ thể để lấy quảng cáo (vistar, cm, lmx, modcart, adtrue)")
            @RequestParam(value = "ssp", required = false) String ssp,
            HttpServletRequest request) {

        // Lấy base URL từ request hiện tại
        String baseUrl = Util.getBaseUrlFromRequest(request);

        if (deviceId == null) {
            deviceId = sspDeviceManager.getDeviceIdFromMac(mac);
        }

        MDC.put("deviceId", deviceId != null ? deviceId : "");
        MDC.put("eventType", "0");
        MDC.put("eventTimestamp", String.valueOf(requestedTimestamp));
        MDC.put("serverTimestamp", String.valueOf(Util.getTimeStampNow()));

        // Get device info
        SspDevice device = sspDeviceManager.getDeviceInfo(deviceId);
        if (device == null) {
            MDC.clear();
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body("<?xml version=\"1.0\" encoding=\"UTF-8\"?><error>Device not found: " + deviceId + "</error>");
        }

        requestedTimestamp = Math.max(requestedTimestamp, Util.getTimeStampNow());

        logger.info("VAST XML request for device: {}", deviceId);

        AdtrueAd ad = adsGettingWorkflow.getAdForDevice(device, requestedTimestamp, ssp);

        if (ad == null || ad.getAdId() == null) {
            logger.info("No ads found for device: {}", deviceId);
            return ResponseEntity.ok("<VAST version=\"2.0\"></VAST>");
        }

        // Log CSV với thông tin đầy đủ
        MDC.put("adId", ad.getAdId());

        // Get necessary info from adInfo
        String assetUrl = ad.getMediaPath();
        if (assetUrl == null) {
            logger.warn("Media path is null for ad: {}", ad.getAdId());
            assetUrl = ""; // Set a default empty string to avoid NullPointerException
        }

        if (!assetUrl.startsWith("http://") && !assetUrl.startsWith("https://")) {
            assetUrl = baseUrl + "/videos/" + ad.getMediaPath();
        }
        // Create URLs for tracking events
        //format như sau: data=[campaign_id]^[banner_id]^[channelid]^[vendorid]^[regionid]^[cityid]^[districtid]^[wardid]^[storeid]^multiplier^[event_type]^[duration]^[ip]^[mac]^[deviceId]

        String baseDataFormat = new StringBuilder().append(baseUrl)
                .append("/vast/log?data=")
                .append(ad.getCampaignId()).append("^")
                .append(ad.getAdId()).append("^")
                .append(device.getChannel_id()).append("^")
                .append(device.getVendor_id()).append("^")
                .append(device.getRegion_id()).append("^")
                .append(device.getCity_id()).append("^")
                .append(device.getDistrict_id()).append("^")
                .append(device.getWard_id()).append("^")
                .append(device.getStore_id()).append("^")
                .append(device.getMultiplier()).append("^")
                .append("%s^")
                .append("[duration]^[ip]^[mac]^")
                .append(deviceId)
                .append("&t=[timestamp]")
                .toString();

        String vastXml = Util.getVastString(baseDataFormat, ad, assetUrl);
        vastEventLogger.info("Vast request {} {}", deviceId, Thread.currentThread().getName());
        MDC.clear();

        kafkaService.sendLogWithDevice(ad.getSspPrefix(), device, ad.getCampaignId(), ad.getAdId(), 0, requestedTimestamp);
        return ResponseEntity.ok(vastXml);
    }



    /**
     * API để ghi log sự kiện video và gửi sang Kafka
     * @param timestamp timestamp của sự kiện
     * @param logData log data của thiết bị
     * @return Phản hồi thành công
     */
    @GetMapping("/log")
    @Operation(summary = "Ghi log sự kiện video", description = "API để ghi log sự kiện video và gửi sang Kafka")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Sự kiện được ghi log thành công"),
        @ApiResponse(responseCode = "500", description = "Lỗi khi ghi log sự kiện")
    })
    public ResponseEntity<String> logVastEvent(
            @Parameter(description = "Data string containing all parameters")
            @RequestParam("data") String logData,
            @Parameter(description = "Timestamp của sự kiện")
            @RequestParam(value = "t", required = false, defaultValue = "0") long timestamp) {
        try {
            // Parse logData để lấy thông tin chi tiết
            String[] parts = logData.split("\\^");
            String adId = parts.length > 1 ? parts[1] : "";
            String eventTypeStr = parts.length > 10 ? parts[10] : "";
            String deviceId = parts.length > 14 ? parts[14] : "";
            long now = Util.getTimeStampNow();
            // Ghi log CSV
            MDC.put("logData", logData);
            MDC.put("eventType", eventTypeStr);
            MDC.put("adId", adId);
            MDC.put("deviceId", deviceId);
            MDC.put("eventTimestamp", String.valueOf(timestamp));

            MDC.put("serverTimestamp", String.valueOf(now));
            vastEventLogger.info("Vast event");
            
            // Cập nhật thời gian request cuối cùng của thiết bị
            sspDeviceManager.updateDeviceHearBeat(deviceId);
            
            // Xác định SSP prefix từ adId
            String sspPrefix = "";
            if (adId != null && !adId.isEmpty()) {
                if (adId.startsWith("vad_")) {
                    sspPrefix = "vad_";
                } else if (adId.startsWith("cm_")) {
                    sspPrefix = "cm_";
                } else if (adId.startsWith("lmx_")) {
                    sspPrefix = "lmx_";
                } else if (adId.startsWith("modcart_")) {
                    sspPrefix = "modcart_";
                }
            }
            
            // Kiểm tra timestamp có quá 24h không
            long currentTime = Util.getTimeStampNow();
            long twentyFourHoursInSeconds = 24 * 60 * 60;
            
            if (currentTime - timestamp > twentyFourHoursInSeconds) {
                // Log vào file riêng nếu event quá 24h
                oldEventsLogger.info("{},{}", logData, timestamp);
            } else {
                // Remove ads từ cache và gửi log request sang Kafka nếu event trong vòng 24h
                adsGettingWorkflow.removeAdFromCache(deviceId, adId);
                kafkaService.sendLogToSspTopic(sspPrefix, logData, timestamp);
            }

            MDC.clear();
            return ResponseEntity.ok("okla");
        } catch (Exception e) {
            logger.error("Error logging vast event: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error logging event: " + e.getMessage());
        }
    }
}
