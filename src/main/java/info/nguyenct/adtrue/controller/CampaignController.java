package info.nguyenct.adtrue.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import info.nguyenct.adtrue.model.CampaignUrlWhitelist;
import info.nguyenct.adtrue.service.CampaignUrlWhitelistService;
import info.nguyenct.adtrue.workflow.OTTCampaignWorkflow;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/campaign")
@Tag(name = "Campaign Management", description = "API quản lý campaign")
public class CampaignController {
    
    private final OTTCampaignWorkflow ottCampaignWorkflow;
    private final CampaignUrlWhitelistService campaignUrlWhitelistService;
    private final ObjectMapper objectMapper;
    
    public CampaignController(OTTCampaignWorkflow ottCampaignWorkflow, ObjectMapper objectMapper,
                            CampaignUrlWhitelistService campaignUrlWhitelistService) {
        this.ottCampaignWorkflow = ottCampaignWorkflow;
        this.campaignUrlWhitelistService = campaignUrlWhitelistService;
        this.objectMapper = objectMapper;
    }

    @PostMapping("/{campaignId}/update")
    @Operation(summary = "Bật campaign. Campaign chạy khi ngày bắt đầu <= ngày hiện tại <= ngày kết thúc và isActive = true")
    public ResponseEntity<String> updateCampaign(
            @Parameter(description = "Campaign ID") @PathVariable String campaignId,
            @Parameter(description = "Campaign có active hay không") @RequestParam(required = true) Boolean isActive,
            @Parameter(description = "Ngày bắt đầu campaign (YYYY-MM-DD)") @RequestParam(required = true) String startDate,
            @Parameter(description = "Ngày kết thúc campaign (YYYY-MM-DD)") @RequestParam(required = true) String endDate) {
        if (campaignId == null || !campaignId.equals("1001200")) {
            return ResponseEntity.badRequest().body("Invalid campaign ID");
        }

        LocalDate start = startDate != null ? LocalDate.parse(startDate) : null;
        LocalDate end = endDate != null ? LocalDate.parse(endDate) : null;

        ottCampaignWorkflow.setActive(isActive, start, end);
        return ResponseEntity.ok("Campaign started");
    }
    
    @PostMapping("/{campaignId}/stop")
    @Operation(summary = "Dừng campaign ngay lập tức")
    public ResponseEntity<String> stopCampaign(
            @Parameter(description = "Campaign ID") @PathVariable String campaignId) {
        if (campaignId == null || !campaignId.equals("1001200")) {
            return ResponseEntity.badRequest().body("Invalid campaign ID");
        }
        ottCampaignWorkflow.setActive(false);
        return ResponseEntity.ok("Campaign stopped");
    }

    @GetMapping("/{campaignId}/status")
    @Operation(summary = "Kiểm tra trạng thái campaign.  Campaign chạy (isRunning=true) khi ngày bắt đầu <= ngày hiện tại <= ngày kết thúc và isActive = true")
    public ResponseEntity<String> getCampaignStatus(
            @Parameter(description = "Campaign ID") @PathVariable String campaignId) {
        if (campaignId == null || !campaignId.equals("1001200")) {
            return ResponseEntity.badRequest().body("Invalid campaign ID");
        }

        Map<String, Object> status = Map.of(
                "isActive", ottCampaignWorkflow.isActive(),
                "isRunning", ottCampaignWorkflow.isRunning(),
                "startDate", ottCampaignWorkflow.getStartDate(),
                "endDate", ottCampaignWorkflow.getEndDate()
        );

        try {
            return ResponseEntity.ok(objectMapper.writeValueAsString(status));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
    
    @GetMapping("/{campaignId}/whitelist")
    @Operation(summary = "Lấy danh sách URL whitelist của campaign")
    public ResponseEntity<List<CampaignUrlWhitelist>> getWhitelist(
            @Parameter(description = "Campaign ID") @PathVariable String campaignId) {
        return ResponseEntity.ok(campaignUrlWhitelistService.getWhitelistByCampaign(campaignId));
    }

    @PostMapping("/{campaignId}/whitelist")
    @Operation(summary = "Thêm 1 danh sách các URL vào whitelist, nếu URL đã tồn tại thì sẽ bỏ qua" +
            ", nếu URL đã tồn tại nhưng không có trong danh sách này sẽ bị xóa",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "Danh sách URL cần thêm vào whitelist",
                content = @io.swagger.v3.oas.annotations.media.Content(
                    mediaType = "application/json",
                    examples = @io.swagger.v3.oas.annotations.media.ExampleObject(
                        value = "[\"https://example.com/video1.mp4\", \"https://example.com/video2.mp4\", \"https://cdn.example.com/ad.mp4\"]"
                    )
                )
            ))
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Thêm thành công"),
        @ApiResponse(responseCode = "400", description = "URL đã tồn tại")
    })
    public ResponseEntity<?> updateWhitelist(
            @Parameter(description = "Campaign ID") @PathVariable String campaignId,
            @Parameter(description = "Base URL cho media path", example = "https://ott.trueview.com.vn/videos/viettel/tvc/") @RequestParam(required = false) String baseUrl,
            @RequestBody List<String> urls) {
        try {
            List<CampaignUrlWhitelist> result = campaignUrlWhitelistService.updateWhitelist(campaignId, urls, baseUrl);
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }

    @GetMapping("/{campaignId}/whitelist/check")
    @Operation(summary = "Kiểm tra URL có được phép không",
            parameters = {
                @Parameter(name = "url", description = "URL cần kiểm tra", 
                    example = "https://example.com/video.mp4")
            })
    public ResponseEntity<Map<String, Boolean>> checkUrl(
            @Parameter(description = "Campaign ID") @PathVariable String campaignId,
            @Parameter(description = "URL cần kiểm tra") @RequestParam String url) {
        boolean allowed = campaignUrlWhitelistService.isUrlAllowed(campaignId, url);
        return ResponseEntity.ok(Map.of("allowed", allowed));
    }
}