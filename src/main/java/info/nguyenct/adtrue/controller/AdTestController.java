package info.nguyenct.adtrue.controller;

import info.nguyenct.adtrue.model.SspDevice;
import info.nguyenct.adtrue.service.SspDeviceManager;
import info.nguyenct.adtrue.service.VastReportInsertService;
import info.nguyenct.adtrue.ssp.cm.CMSsp;
import info.nguyenct.adtrue.ssp.vistar.VistarSsp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/test")
@Tag(name = "Ad Testing API", description = "APIs for testing ad processing functionality")
public class AdTestController {

    private final VastReportInsertService vastReportInsertService;
    private final SspDeviceManager sspDeviceManager;
    private final VistarSsp vistarSsp;
    private final CMSsp cmSsp;

    public AdTestController(VistarSsp vistarSsp, CMSsp cmSsp,
                           SspDeviceManager sspDeviceManager, VastReportInsertService vastReportInsertService) {
        this.vistarSsp = vistarSsp;
        this.sspDeviceManager = sspDeviceManager;
        this.cmSsp = cmSsp;
        this.vastReportInsertService = vastReportInsertService;
    }

    @GetMapping("/devices")
    @Operation(summary = "Retrieves all available SSP devices")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved devices",
                content = @Content(mediaType = "application/json", 
                schema = @Schema(implementation = SspDevice.class))),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<List<SspDevice>> getAllDevices() {
        try {
            List<SspDevice> devices = sspDeviceManager.getAllDevices();
            return ResponseEntity.ok(devices);
        } catch (Exception e) {
            return ResponseEntity.status(500).build();
        }
    }


    // Add new endpoint to manually load CM inventory
    @PostMapping("/load_cm_inventory")
    @Operation(summary = "Manually load inventory from CM")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully loaded CM inventory",
                    content = @Content(mediaType = "application/json")),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Map<String, Object>> loadCMInventory() {
        try {
            cmSsp.loadInventoryFromCM();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "CM inventory load initiated successfully");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Error loading CM inventory: " + e.getMessage());

            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * Determines SSP type from ad ID prefix
     */
    private String getSspTypeFromAdId(String adId) {
        if (adId.startsWith("vad_")) {
            return "vistar";
        } else if (adId.startsWith("cm_")) {
            return "cm";
        } else {
            return "adtrue";
        }
    }

    @PostMapping("/cache_creatives")
    @Operation(summary = "Manually trigger creative caching for devices")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully triggered creative caching",
                    content = @Content(mediaType = "application/json")),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Map<String, Object>> cacheCreatives() {
        try {
            Map<String, Object> response = new HashMap<>();

            // Cache for all devices
            List<SspDevice> devices = sspDeviceManager.getAllDevices();
            int totalDevices = devices.size();
            int totalCachedCreatives = 0;

            for (SspDevice device : devices) {
                totalCachedCreatives += vistarSsp.cacheCreativesForDevice(device);
            }

            response.put("success", true);
            response.put("message", "Creative caching triggered for all devices");
            response.put("deviceCount", totalDevices);
            response.put("totalCachedCreatives", totalCachedCreatives);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Error triggering creative caching: " + e.getMessage());
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    @GetMapping("/insert_vast_reports")
    public ResponseEntity<String> getVastReports() {
//        vastReportInsertService.insertFirstData();
        return ResponseEntity.ok("ok");
    }
}
