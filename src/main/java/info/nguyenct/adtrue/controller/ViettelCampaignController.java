package info.nguyenct.adtrue.controller;

import info.nguyenct.adtrue.model.AdtrueAd;
import info.nguyenct.adtrue.model.SspDevice;
import info.nguyenct.adtrue.service.KafkaService;
import info.nguyenct.adtrue.service.RedisService;
import info.nguyenct.adtrue.util.Util;
import info.nguyenct.adtrue.workflow.OTTCampaignWorkflow;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequestMapping("/vt_vast")
@Tag(name = "Viettel Vast API", description = "Viettel Vast api cho OTT Campaign")
public class ViettelCampaignController {
    private static final Logger logger = LoggerFactory.getLogger(ViettelCampaignController.class);
    private static final Logger ottEventLogger = LoggerFactory.getLogger("ott_vast_events");
    
    private final OTTCampaignWorkflow ottCampaignWorkflow;
    private final RedisService redisService;
    private final KafkaService kafkaService;

    public ViettelCampaignController(OTTCampaignWorkflow ottCampaignWorkflow, RedisService redisService, KafkaService kafkaService) {
        this.ottCampaignWorkflow = ottCampaignWorkflow;
        this.redisService = redisService;
        this.kafkaService = kafkaService;
    }

    @GetMapping(value = "/getxml", produces = MediaType.APPLICATION_XML_VALUE)
    @Operation(summary = "Lấy VAST XML cho OTT device")
    public ResponseEntity<String> getOTTVast(
            @Parameter(description = "Device ID từ đối tác OTT") 
            @RequestParam("did") String deviceId,
            @RequestParam(value = "campaign", required = false) String campaignId,
            @RequestParam("banner") String bannerId,
            HttpServletRequest request) {

        String baseUrl = Util.getBaseUrlFromRequest(request);
        long timestamp = Util.getTimeStampNow();
        
        MDC.put("deviceId", deviceId);
        MDC.put("eventType", "0");
        MDC.put("serverTimestamp", String.valueOf(timestamp));

        logger.info("OTT VAST request for device: {}", deviceId);
        
        AdtrueAd ad = ottCampaignWorkflow.getAdForDevice(deviceId, "1001200", bannerId);

        if (ad == null || ad.getAdId() == null) {
            logger.info("No OTT ads found for device: {}", deviceId);
            MDC.clear();
            return ResponseEntity.ok("<VAST version=\"2.0\"></VAST>");
        }

        MDC.put("adId", ad.getAdId());

        String assetUrl = ad.getMediaPath();
        if (!assetUrl.startsWith("http://") && !assetUrl.startsWith("https://")) {
            assetUrl = baseUrl + "/videos/viettel/" + ad.getMediaPath();
        }

        // Create URLs for tracking events
        //format như sau: data=[campaign_id]^[banner_id]^[channelid]^[vendorid]^[regionid]^[cityid]^[districtid]^[wardid]^[storeid]^multiplier^[event_type]^[duration]^[ip]^[mac]^[deviceId]

        String baseDataFormat = new StringBuilder().append(baseUrl)
                .append("/vt_vast/log?data=")
                .append(bannerId).append("^")
                .append(ad.getAdId()).append("^")
                .append(0).append("^")
                .append(0).append("^")
                .append(0).append("^")
                .append(0).append("^")
                .append(0).append("^")
                .append(0).append("^")
                .append(0).append("^")
                .append(0).append("^")
                .append("%s^")
                .append("[duration]^[ip]^[mac]^")
                .append(deviceId)
                .append("&cb=[CACHEBUSTING]")
                .toString();

        String vastXml = Util.getVastString(baseDataFormat, ad, assetUrl);
        ottEventLogger.info("Vast request {} {}", deviceId, Thread.currentThread().getName());
        MDC.clear();

        kafkaService.sendLogWithDevice(ad.getSspPrefix(), new SspDevice(deviceId), ad.getCampaignId(), ad.getAdId(), 0, Util.getTimeStampNow());
        return ResponseEntity.ok(vastXml);
    }


    @GetMapping("/log")
    @Operation(summary = "Ghi log sự kiện video", description = "API để ghi log sự kiện video và gửi sang Kafka")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Sự kiện được ghi log thành công"),
            @ApiResponse(responseCode = "500", description = "Lỗi khi ghi log sự kiện")
    })
    public ResponseEntity<String> logVastEvent(
            @Parameter(description = "Data string containing all parameters")
            @RequestParam("data") String logData,
            @Parameter(description = "Timestamp của sự kiện")
            @RequestParam(value = "cb", required = false, defaultValue = "0") String timestamp) {
        try {
            // Parse logData để lấy thông tin chi tiết
            String[] parts = logData.split("\\^");
            String adId = parts.length > 1 ? parts[1] : "";
            String bannerId = parts.length > 0 ? parts[0] : "";
            String eventTypeStr = parts.length > 10 ? parts[10] : "";
            String deviceId = parts.length > 14 ? parts[14] : "";
            long now = Util.getTimeStampNow();
            // Ghi log CSV
            MDC.put("logData", logData);
            MDC.put("eventType", eventTypeStr);
            MDC.put("adId", adId);
            MDC.put("deviceId", deviceId);
            MDC.put("eventTimestamp", String.valueOf(timestamp));

            MDC.put("serverTimestamp", String.valueOf(now));
            ottEventLogger.info("Vast event");

            long eventTimestamp = now;
            try {
                eventTimestamp = Long.parseLong(timestamp);
            } catch (NumberFormatException e) {
                logger.warn("Invalid timestamp format: {}", timestamp);
            }

            ottCampaignWorkflow.removeAdFromCache(deviceId, bannerId, adId);
            kafkaService.sendLogToSspTopic("modcart_", logData, eventTimestamp);

            MDC.clear();
            return ResponseEntity.ok("okla");
        } catch (Exception e) {
            logger.error("Error logging vast event: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error logging event: " + e.getMessage());
        }
    }
}