package info.nguyenct.adtrue.controller;

import info.nguyenct.adtrue.dto.BannerReportResponse;
import info.nguyenct.adtrue.dto.CampaignReportResponse;
import info.nguyenct.adtrue.dto.DetailReportResponse;
import info.nguyenct.adtrue.dto.DeviceReportResponse;
import info.nguyenct.adtrue.repository.JdbcVastReportRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;
import java.util.Arrays;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/report/cm")
public class VastReportController {

    private final JdbcVastReportRepository reportRepository;

    public VastReportController(JdbcVastReportRepository reportRepository) {
        this.reportRepository = reportRepository;
    }

    @GetMapping("/banner")
    @Operation(summary = "Get banner report", description = "Get report grouped by banner_id")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Banner report retrieved successfully")
    })
    public ResponseEntity<List<BannerReportResponse>> getBannerReport(
            @Parameter(description = "Start date (YYYY-MM-DD), default: 1970-01-01")
            @RequestParam(value = "fdate", required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fdate,

            @Parameter(description = "End date (YYYY-MM-DD), default: 2040-01-01")
            @RequestParam(value = "tdate", required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate tdate,

            @Parameter(description = "Banner IDs separated by comma (e.g., 123,456,789)")
            @RequestParam(value = "banner_id", required = false) String bannerIds,

            @Parameter(description = "Channel ID - Optional filter, combined with AND logic")
            @RequestParam(value = "channel_id", required = false) Integer channelId,

            @Parameter(description = "Vendor ID - Optional filter, combined with AND logic")
            @RequestParam(value = "vendor_id", required = false) Integer vendorId,

            @Parameter(description = "Region ID - Optional filter, combined with AND logic")
            @RequestParam(value = "region_id", required = false) Integer regionId,

            @Parameter(description = "City ID - Optional filter, combined with AND logic")
            @RequestParam(value = "city_id", required = false) Integer cityId,

            @Parameter(description = "District ID - Optional filter, combined with AND logic")
            @RequestParam(value = "district_id", required = false) Integer districtId,

            @Parameter(description = "Ward ID - Optional filter, combined with AND logic")
            @RequestParam(value = "ward_id", required = false) Integer wardId,

            @Parameter(description = "Store ID - Optional filter, combined with AND logic")
            @RequestParam(value = "store_id", required = false) Integer storeId) {

        // Set default dates
        if (fdate == null) {
            fdate = LocalDate.of(1970, 1, 1);
        }
        if (tdate == null) {
            tdate = LocalDate.of(2040, 1, 1);
        }

        // Parse banner IDs
        List<String> bannerIdList = null;
        if (bannerIds != null) {
            bannerIdList = Arrays.asList(bannerIds.split(","));
        }

        // Build filters map
        Map<String, Object> filters = buildFiltersMap(channelId, vendorId, regionId, cityId, districtId, wardId, storeId);

        List<BannerReportResponse> reports = reportRepository.getBannerReport(fdate, tdate, bannerIdList, filters);
        return ResponseEntity.ok(reports);
    }

    @GetMapping("/campaign")
    @Operation(summary = "Get campaign report", description = "Get report grouped by campaign_id")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Campaign report retrieved successfully")
    })
    public ResponseEntity<List<CampaignReportResponse>> getCampaignReport(
            @Parameter(description = "Start date (YYYY-MM-DD), default: 1970-01-01")
            @RequestParam(value = "fdate", required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fdate,

            @Parameter(description = "End date (YYYY-MM-DD), default: 2040-01-01")
            @RequestParam(value = "tdate", required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate tdate,

            @Parameter(description = "Campaign IDs separated by comma (e.g., 123,456,789)")
            @RequestParam(value = "campaign_id", required = false) String campaignIds,

            @Parameter(description = "Channel ID - Optional filter, combined with AND logic")
            @RequestParam(value = "channel_id", required = false) Integer channelId,

            @Parameter(description = "Vendor ID - Optional filter, combined with AND logic")
            @RequestParam(value = "vendor_id", required = false) Integer vendorId,

            @Parameter(description = "Region ID - Optional filter, combined with AND logic")
            @RequestParam(value = "region_id", required = false) Integer regionId,

            @Parameter(description = "City ID - Optional filter, combined with AND logic")
            @RequestParam(value = "city_id", required = false) Integer cityId,

            @Parameter(description = "District ID - Optional filter, combined with AND logic")
            @RequestParam(value = "district_id", required = false) Integer districtId,

            @Parameter(description = "Ward ID - Optional filter, combined with AND logic")
            @RequestParam(value = "ward_id", required = false) Integer wardId,

            @Parameter(description = "Store ID - Optional filter, combined with AND logic")
            @RequestParam(value = "store_id", required = false) Integer storeId) {

        // Set default dates
        if (fdate == null) {
            fdate = LocalDate.of(1970, 1, 1);
        }
        if (tdate == null) {
            tdate = LocalDate.of(2040, 1, 1);
        }

        // Parse campaign IDs
        List<String> campaignIdList = null;
        if (campaignIds != null) {
            campaignIdList = Arrays.asList(campaignIds.split(","));
        }

        // Build filters map
        Map<String, Object> filters = buildFiltersMap(channelId, vendorId, regionId, cityId, districtId, wardId, storeId);

        List<CampaignReportResponse> reports = reportRepository.getCampaignReport(fdate, tdate, campaignIdList, filters);
        return ResponseEntity.ok(reports);
    }

    @GetMapping("/campaign/detail")
    @Operation(summary = "Get detailed campaign report", description = "Get detailed report for a specific campaign with grouping options")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Campaign detail report retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid group_by parameter")
    })
    public ResponseEntity<?> getCampaignDetailReport(
            @Parameter(description = "Start date (YYYY-MM-DD), default: 1970-01-01")
            @RequestParam(value = "fdate", required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fdate,

            @Parameter(description = "End date (YYYY-MM-DD), default: 2040-01-01")
            @RequestParam(value = "tdate", required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate tdate,

            @Parameter(description = "Campaign ID (required)")
            @RequestParam(value = "campaign_id", required = true) String campaignId,

            @Parameter(description = "Group by field (day, banner_id, channel_id, vendor_id, region_id, city_id, district_id, ward_id, store_id, device_id)")
            @RequestParam(value = "group_by", required = true) String groupBy,

            @Parameter(description = "Channel ID - Optional filter")
            @RequestParam(value = "channel_id", required = false) Integer channelId,

            @Parameter(description = "Vendor ID - Optional filter")
            @RequestParam(value = "vendor_id", required = false) Integer vendorId,

            @Parameter(description = "Region ID - Optional filter")
            @RequestParam(value = "region_id", required = false) Integer regionId,

            @Parameter(description = "City ID - Optional filter")
            @RequestParam(value = "city_id", required = false) Integer cityId,

            @Parameter(description = "District ID - Optional filter")
            @RequestParam(value = "district_id", required = false) Integer districtId,

            @Parameter(description = "Ward ID - Optional filter")
            @RequestParam(value = "ward_id", required = false) Integer wardId,

            @Parameter(description = "Store ID - Optional filter")
            @RequestParam(value = "store_id", required = false) Integer storeId) {

        // Set default dates
        if (fdate == null) {
            fdate = LocalDate.of(1970, 1, 1);
        }
        if (tdate == null) {
            tdate = LocalDate.of(2040, 1, 1);
        }

        // Validate group_by parameter
        List<String> validGroupByFields = Arrays.asList(
                "day", "banner_id", "channel_id", "vendor_id", "region_id",
                "city_id", "district_id", "ward_id", "store_id", "device_id"
        );

        if (!validGroupByFields.contains(groupBy)) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Invalid group_by parameter");
            errorResponse.put("message", "group_by must be one of: " + String.join(", ", validGroupByFields));
            return ResponseEntity.badRequest().body(errorResponse);
        }

        // Build filters map
        Map<String, Object> filters = buildFiltersMap(channelId, vendorId, regionId, cityId, districtId, wardId, storeId);

        // Add campaign_id to filters
        filters.put("campaign_id", campaignId);

        List<DetailReportResponse> reports = reportRepository.getCampaignDetailReport(fdate, tdate, campaignId, groupBy, filters);
        return ResponseEntity.ok(reports);
    }

    @GetMapping("/banner/detail")
    @Operation(summary = "Get detailed banner report", description = "Get detailed report for a specific banner with grouping options")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Banner detail report retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid group_by parameter")
    })
    public ResponseEntity<?> getBannerDetailReport(
            @Parameter(description = "Start date (YYYY-MM-DD), default: 1970-01-01")
            @RequestParam(value = "fdate", required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fdate,

            @Parameter(description = "End date (YYYY-MM-DD), default: 2040-01-01")
            @RequestParam(value = "tdate", required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate tdate,

            @Parameter(description = "Banner ID (required)")
            @RequestParam(value = "banner_id", required = true) String bannerId,

            @Parameter(description = "Group by field (day, channel_id, vendor_id, region_id, city_id, district_id, ward_id, store_id, device_id)")
            @RequestParam(value = "group_by", required = true) String groupBy,

            @Parameter(description = "Channel ID - Optional filter")
            @RequestParam(value = "channel_id", required = false) Integer channelId,

            @Parameter(description = "Vendor ID - Optional filter")
            @RequestParam(value = "vendor_id", required = false) Integer vendorId,

            @Parameter(description = "Region ID - Optional filter")
            @RequestParam(value = "region_id", required = false) Integer regionId,

            @Parameter(description = "City ID - Optional filter")
            @RequestParam(value = "city_id", required = false) Integer cityId,

            @Parameter(description = "District ID - Optional filter")
            @RequestParam(value = "district_id", required = false) Integer districtId,

            @Parameter(description = "Ward ID - Optional filter")
            @RequestParam(value = "ward_id", required = false) Integer wardId,

            @Parameter(description = "Store ID - Optional filter")
            @RequestParam(value = "store_id", required = false) Integer storeId) {

        // Set default dates
        if (fdate == null) {
            fdate = LocalDate.of(1970, 1, 1);
        }
        if (tdate == null) {
            tdate = LocalDate.of(2040, 1, 1);
        }

        // Validate group_by parameter
        List<String> validGroupByFields = Arrays.asList(
                "day", "channel_id", "vendor_id", "region_id",
                "city_id", "district_id", "ward_id", "store_id", "device_id"
        );

        if (!validGroupByFields.contains(groupBy)) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Invalid group_by parameter");
            errorResponse.put("message", "group_by must be one of: " + String.join(", ", validGroupByFields));
            return ResponseEntity.badRequest().body(errorResponse);
        }

        // Build filters map
        Map<String, Object> filters = buildFiltersMap(channelId, vendorId, regionId, cityId, districtId, wardId, storeId);

        // Add banner_id to filters
        filters.put("banner_id", bannerId);

        List<DetailReportResponse> reports = reportRepository.getBannerDetailReport(fdate, tdate, bannerId, groupBy, filters);
        return ResponseEntity.ok(reports);
    }

    @GetMapping("/device-detail-report")
    @Operation(summary = "Lấy báo cáo chi tiết theo thiết bị",
               description = "Trả về báo cáo chi tiết theo thiết bị với khả năng group theo ngày hoặc giờ")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Báo cáo được tạo thành công"),
        @ApiResponse(responseCode = "400", description = "Tham số không hợp lệ")
    })
    public ResponseEntity<?> getDeviceDetailReport(
            @Parameter(description = "Ngày bắt đầu (YYYY-MM-DD)")
            @RequestParam(value = "fdate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fdate,

            @Parameter(description = "Ngày kết thúc (YYYY-MM-DD)")
            @RequestParam(value = "tdate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate tdate,

            @Parameter(description = "Campaign ID - Optional filter")
            @RequestParam(value = "campaign_id", required = false) String campaignId,

            @Parameter(description = "Banner ID - Optional filter")
            @RequestParam(value = "banner_id", required = false) String bannerId,

            @Parameter(description = "Nhóm theo: day (theo ngày) hoặc hour (theo giờ)")
            @RequestParam(value = "group_by") String groupBy,

            @Parameter(description = "Channel ID - Optional filter, combined with AND logic")
            @RequestParam(value = "channel_id", required = false) Integer channelId,

            @Parameter(description = "Vendor ID - Optional filter, combined with AND logic")
            @RequestParam(value = "vendor_id", required = false) Integer vendorId,

            @Parameter(description = "Region ID - Optional filter, combined with AND logic")
            @RequestParam(value = "region_id", required = false) Integer regionId,

            @Parameter(description = "City ID - Optional filter, combined with AND logic")
            @RequestParam(value = "city_id", required = false) Integer cityId,

            @Parameter(description = "District ID - Optional filter, combined with AND logic")
            @RequestParam(value = "district_id", required = false) Integer districtId,

            @Parameter(description = "Ward ID - Optional filter, combined with AND logic")
            @RequestParam(value = "ward_id", required = false) Integer wardId,

            @Parameter(description = "Store ID - Optional filter, combined with AND logic")
            @RequestParam(value = "store_id", required = false) Integer storeId) {

        // Set default dates
        if (fdate == null) {
            fdate = LocalDate.of(1970, 1, 1);
        }
        if (tdate == null) {
            tdate = LocalDate.of(2040, 1, 1);
        }

        // Validate group_by parameter - only allow day and hour
        List<String> validGroupByFields = Arrays.asList("day", "hour");
        if (!validGroupByFields.contains(groupBy)) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Invalid group_by parameter");
            errorResponse.put("message", "group_by must be one of: " + String.join(", ", validGroupByFields));
            return ResponseEntity.badRequest().body(errorResponse);
        }

        // Build filters map
        Map<String, Object> filters = buildFiltersMap(channelId, vendorId, regionId, cityId, districtId, wardId, storeId);

        List<DeviceReportResponse> reports = reportRepository.getDeviceDetailReport(fdate, tdate, campaignId, bannerId, groupBy, filters);
        return ResponseEntity.ok(reports);
    }

    /**
     * Helper method to build filters map from request parameters
     */
    private Map<String, Object> buildFiltersMap(Integer channelId, Integer vendorId, Integer regionId,
                                                Integer cityId, Integer districtId, Integer wardId, Integer storeId) {
        Map<String, Object> filters = new HashMap<>();

        if (channelId != null) {
            filters.put("channel_id", channelId);
        }
        if (vendorId != null) {
            filters.put("vendor_id", vendorId);
        }
        if (regionId != null) {
            filters.put("region_id", regionId);
        }
        if (cityId != null) {
            filters.put("city_id", cityId);
        }
        if (districtId != null) {
            filters.put("district_id", districtId);
        }
        if (wardId != null) {
            filters.put("ward_id", wardId);
        }
        if (storeId != null) {
            filters.put("store_id", storeId);
        }

        return filters;
    }
}