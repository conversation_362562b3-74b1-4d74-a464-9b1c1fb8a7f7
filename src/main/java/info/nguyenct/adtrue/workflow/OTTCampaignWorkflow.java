package info.nguyenct.adtrue.workflow;

import info.nguyenct.adtrue.model.AdtrueAd;
import info.nguyenct.adtrue.service.RedisService;
import info.nguyenct.adtrue.ssp.modcart.ModcartSsp;
import info.nguyenct.adtrue.util.Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Service
public class OTTCampaignWorkflow {
    private static final Logger logger = LoggerFactory.getLogger(OTTCampaignWorkflow.class);
    private boolean isActive = true;
    private LocalDate startDate;
    private LocalDate endDate;

    private final ModcartSsp modcartSsp;
    private final RedisService redisService;

    public OTTCampaignWorkflow(ModcartSsp modcartSsp, RedisService redisService) {
        this.modcartSsp = modcartSsp;
        this.redisService = redisService;
    }

    public void setActive(boolean active) {
        this.isActive = active;
    }

    public void setActive(boolean active, LocalDate startDate, LocalDate endDate) {
        this.isActive = active;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public boolean isRunning() {
        if (!isActive) {
            return false;
        }

        LocalDate today = LocalDate.now();
        if (startDate != null && today.isBefore(startDate)) {
            return false;
        }

        if (endDate != null && today.isAfter(endDate)) {
            return false;
        }

        return true;
    }

    public boolean isActive() {
        return isActive;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public AdtrueAd getAdForDevice(String deviceId, String campaignId, String bannerId) {
        // check if campaign is running, if not return null
        if (!isRunning()) {
            return null;
        }

        // Check cache by deviceId and bannerId, if cache not exists, get from modcartSsp and put to cache
        String cacheKey = "viettel:vast:" + deviceId + ":" + bannerId;
        AdtrueAd ad = redisService.getCachedAd(cacheKey);

        if (ad == null) {
            logger.info("Cache miss for device: {} banner: {}, fetching from modcart", deviceId, bannerId);
            ad = modcartSsp.getVastAdFromUrl(deviceId, getOTTUrl(bannerId, deviceId));
            if (ad != null) {
                ad.setCampaignId(campaignId);
                ad.setBannerId(bannerId);
                redisService.cacheAd(cacheKey, ad, 15);
                redisService.saveAdtrueAd(ad);
            }
        }
        return ad;

    }

    public void removeAdFromCache(String deviceId, String bannerId, String adId) {
        String cacheKey = "viettel:vast:" + deviceId + ":" + bannerId;
        AdtrueAd ad = redisService.getCachedAd(cacheKey);
        if (ad != null && ad.getAdId().equals(adId)) {
            logger.info("Removing ad {} from cache for device {}", adId, deviceId);
            redisService.removeAdFromCache(cacheKey);
        }
    }

    private String getOTTUrl(String bannerId, String deviceId) {
        String urlFormat = null;
        if (bannerId.equalsIgnoreCase("1990013")) {
            urlFormat = "https://rtb-apac-vast.zeststack.com/bid?rtb_seat_id=01750&secret_key=oaELtHa&appid=1750&type=vast&env=web&pubid=1750&width=1920&height=1080&domain=tv360.vn&r=https%3A%2F%2Ftv360.vn&coppa=&gdpr=&gcs=&acm=&uac=&c=&dnt=0&desc=https%3A%2F%2Ftv360.vn&dur=&cb=%%CACHEBUSTER%%";
        }else if (bannerId.equalsIgnoreCase("1990014")) {
            urlFormat = "https://rtb-apac-vast.zeststack.com/bid?rtb_seat_id=01750&pubid=1750&appid=1750&secret_key=oaELtHa&type=vast&env=app&device_type=4&bundle=1536071266&store=https%3A%2F%2Fapps.apple.com%2Fid%2Fapp%2Ftv360-truy%25E1%25BB%2581n-h%25C3%25ACnh-tr%25E1%25BB%25B1c-tuy%25E1%25BA%25BFn%2Fid1536071266%3Fl%3Did&developer=tv360.vn&app_name=TV360%20%E2%80%93%20Truy%E1%BB%81n%20h%C3%ACnh%20tr%E1%BB%B1c%20tuy%E1%BA%BFn&width=1920&height=1080&did=%%ADVERTISING_IDENTIFIER_PLAIN%%&dtype=idfa&lat=&coppa=&gdpr=&gcs=&acm=&uac=&c=&dnt=0&desc=&dur=&cb=%%CACHEBUSTER%%";
        }else if (bannerId.equalsIgnoreCase("1990015")) {
            urlFormat = "https://rtb-apac-vast.zeststack.com/bid?rtb_seat_id=01750&pubid=1750&appid=1750&secret_key=oaELtHa&type=vast&env=app&device_type=4&bundle=com.viettel.tv360&store=https%3A%2F%2Fplay.google.com%2Fstore%2Fapps%2Fdetails%3Fid%3Dcom.viettel.tv360&developer=tv360.vn&app_name=TV360%20%E2%80%93%20Truy%E1%BB%81n%20h%C3%ACnh%20tr%E1%BB%B1c%20tuy%E1%BA%BFn&width=1920&height=1080&did=%%ADVERTISING_IDENTIFIER_PLAIN%%&dtype=adid&lat=&coppa=&gdpr=&gcs=&acm=&uac=&c=&dnt=0&desc=&dur=&cb=%%CACHEBUSTER%%";
        }else if (bannerId.equalsIgnoreCase("1990016")) {
            urlFormat = "https://rtb-apac-vast.zeststack.com/bid?rtb_seat_id=02739&pubid=2739&appid=2739&secret_key=oaELtHaCTV&type=vast&env=app&device_type=3&bundle=com.viettel.tv360.tv&store=https%3A%2F%2Fplay.google.com%2Fstore%2Fapps%2Fdetails%3Fid%3Dcom.viettel.tv360.tv&developer=tv360.vn&app_name=TV360%20SmartTV&width=1920&height=1080&did=%%ADVERTISING_IDENTIFIER_PLAIN%%&dtype=adid&lat=&coppa=&gdpr=&gcs=&acm=&uac=&c=&dnt=0&desc=&dur=&cb=%%CACHEBUSTER%%";
        }
        return urlFormat.replace("%%ADVERTISING_IDENTIFIER_PLAIN%%", deviceId).replace("%%CACHEBUSTER%%", String.valueOf(Util.getTimeStampUTCNow()));
    }
}
