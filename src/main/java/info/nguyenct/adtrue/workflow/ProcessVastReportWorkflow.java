package info.nguyenct.adtrue.workflow;

import info.nguyenct.adtrue.model.AdtrueAd;
import info.nguyenct.adtrue.model.VastEventType;
import info.nguyenct.adtrue.model.VastLogRequest;
import info.nguyenct.adtrue.model.VastReport;
import info.nguyenct.adtrue.repository.JdbcVastReportRepository;
import info.nguyenct.adtrue.service.KafkaService;
import info.nguyenct.adtrue.service.RedisService;
import info.nguyenct.adtrue.ssp.Ssp;
import info.nguyenct.adtrue.ssp.SspManager;
import info.nguyenct.adtrue.util.Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class ProcessVastReportWorkflow {
    private static final Logger logger = LoggerFactory.getLogger(ProcessVastReportWorkflow.class);
    private static final String CONSUMER_GROUP = "vast-report-8";
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    private static final String AD_START_TIMESTAMP_PREFIX = "vast:ad_start_timestamp:";
    
    private final KafkaService kafkaService;
    private final JdbcVastReportRepository vastReportRepository;
    private final SspManager sspManager;
    private final RedisService redisService;

    // Cache in-memory để tăng tốc độ truy cập
    private static final Map<String, Long> adStartTimestampCache = new ConcurrentHashMap<>();
    
    // Thời gian cuối cùng đồng bộ với Redis
    private static long lastSyncTime = 0;
    // Khoảng thời gian đồng bộ với Redis (5 phút)
    private static final long SYNC_INTERVAL = 5 * 60 * 1000;

    public ProcessVastReportWorkflow(KafkaService kafkaService, SspManager sspManager,
                                    JdbcVastReportRepository vastReportRepository,
                                    RedisService redisService) {
        this.kafkaService = kafkaService;
        this.vastReportRepository = vastReportRepository;
        this.sspManager = sspManager;
        this.redisService = redisService;
    }

    @Scheduled(cron = "0 */5 * * * *")
    public void processVastReportEvents() {
        logger.info("Starting VAST report generation");
        
        // Key: deviceId:sspName:campaignId:date:channel:vendor:region:city:district:ward:store:multiplier, Value: VastReport
        Map<String, VastReport> reportMap = new HashMap<>();
        
        // Load adStartTimestamp from Redis only if cache is empty or on application start
        if (adStartTimestampCache.isEmpty()) {
            loadAdStartTimestampsFromRedis();
            logger.info("Loaded {} ad start timestamps from Redis", adStartTimestampCache.size());
        }
        
        // Read logs from Kafka
        for (int i = 1; i < 21; i++) {
            List<String> logs = kafkaService.readAllVideoLogs(CONSUMER_GROUP);
            logger.info("Fetched iteration {}: reads {} video log requests from Kafka", i, logs.size());

            for (String log : logs) {
                try {
                    VastLogRequest logRequest = Util.fromLogString(log);
                    if (logRequest == null) {
                        logger.warn("Failed to parse log: {}", log);
                        continue;
                    }

                    String deviceId = logRequest.getDeviceId();
                    String adId = logRequest.getAdId();

                    if (deviceId == null || adId == null) {
                        continue;
                    }

                    // bỏ qua những event không quan tâm
                    if (logRequest.getEvent() == VastEventType.UNKNOWN) {
                        continue;
                    }

                    AdtrueAd adtrueAd = redisService.getAdtrueAd(adId);
                    if (adtrueAd == null) {
                        logger.warn("No adtrueAd information found for adId: {}", adId);
                        continue;
                    }
                    String campaignId = adtrueAd.getCampaignId();
                    String bannerId = adtrueAd.getBannerId();
                    
                    // Determine SSP name from adId prefix
                    Ssp ssp = sspManager.getSspForAd(adId);
                    String sspName = ssp != null ? ssp.sspName() : "unknown";

                    // Convert timestamp to LocalDateTime
                    LocalDateTime dateTime = LocalDateTime.ofInstant(
                            Instant.ofEpochSecond(logRequest.getTimestamp()),
                            ZoneId.systemDefault());

                    //truncate to each 10 minutes
                    LocalDateTime eventDate = dateTime.withMinute((dateTime.getMinute() / 10) * 10).withSecond(0).withNano(0);

                    // Create composite key with all dimensions
                    String key = String.join(":",
                            deviceId,
                            sspName,
                            campaignId,
                            bannerId,
                            eventDate.format(DATE_TIME_FORMATTER),
                            String.valueOf(logRequest.getChannelId()),
                            String.valueOf(logRequest.getVendorId()),
                            String.valueOf(logRequest.getRegionId()),
                            String.valueOf(logRequest.getCityId()),
                            String.valueOf(logRequest.getDistrictId()),
                            String.valueOf(logRequest.getWardId()),
                            String.valueOf(logRequest.getStoreId()),
                            String.valueOf(logRequest.getMultiplier())
                    );

                    // Get or create report
                    VastReport report = reportMap.computeIfAbsent(key, k -> {
                        VastReport newReport = new VastReport();
                        newReport.setDeviceId(deviceId);
                        newReport.setSspName(sspName);
                        newReport.setCampaignId(campaignId);
                        newReport.setBannerId(bannerId);
                        newReport.setEventDate(eventDate);
                        newReport.setChannelId(logRequest.getChannelId());
                        newReport.setVendorId(logRequest.getVendorId());
                        newReport.setRegionId(logRequest.getRegionId());
                        newReport.setCityId(logRequest.getCityId());
                        newReport.setDistrictId(logRequest.getDistrictId());
                        newReport.setWardId(logRequest.getWardId());
                        newReport.setStoreId(logRequest.getStoreId());
                        newReport.setMultiplier(logRequest.getMultiplier());
                        newReport.setTime(LocalDateTime.now());
                        return newReport;
                    });

                    // Update metrics based on event type
                    switch (logRequest.getEvent()) {
                        case VAST_REQUEST:
                            report.setTotalVastRequest(report.getTotalVastRequest() + 1);
                            break;
                        case IMPRESSION:
                            report.setTotalImpression(report.getTotalImpression() + 1);
                            break;
                        case CREATIVE_VIEW:
                            report.setTotalCreativeView(report.getTotalCreativeView() + 1);
                            break;
                        case START:
                            report.setTotalStart(report.getTotalStart() + 1);
                            adStartTimestampCache.put(adId, logRequest.getTimestamp());
                            break;
                        case FIRST_QUARTILE:
                            report.setTotalFirstQuartile(report.getTotalFirstQuartile() + 1);
                            break;
                        case MIDPOINT:
                            report.setTotalMidpoint(report.getTotalMidpoint() + 1);
                            break;
                        case THIRD_QUARTILE:
                            report.setTotalThirdQuartile(report.getTotalThirdQuartile() + 1);
                            break;
                        case COMPLETE:
                            report.setTotalComplete(report.getTotalComplete() + 1);
                            if (adStartTimestampCache.containsKey(adId)) {
                                long duration = logRequest.getTimestamp() - adStartTimestampCache.get(adId);
                                logRequest.setDuration((int) duration);
                                adStartTimestampCache.remove(adId);
                            }
                            report.setTotalDuration(report.getTotalDuration() + logRequest.getDuration());
                            break;
                        case CLICK_THROUGH:
                            report.setTotalClickThrough(report.getTotalClickThrough() + 1);
                            break;
                        case ERROR:
                            report.setTotalError(report.getTotalError() + 1);
                            break;
                    }

                    // Update timestamp
                    report.setTime(LocalDateTime.now());
                } catch (Exception e) {
                    logger.error("Error processing log: {}", e.getMessage(), e);
                }
            }

            // If not enough logs, break early
            if (logs.size() < 1000) {
                break;
            }
        }

        saveReport(reportMap);
        
        // Đồng bộ với Redis theo định kỳ hoặc khi kết thúc
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastSyncTime > SYNC_INTERVAL) {
            saveAdStartTimestampsToRedis();
            lastSyncTime = currentTime;
        }

        logger.info("Completed VAST report generation, processed {} reports", reportMap.size());
    }

    /**
     * Lưu báo cáo vào cơ sở dữ liệu sử dụng batch save
     * @param reportMap Map chứa các báo cáo cần lưu
     */
    private void saveReport(Map<String, VastReport> reportMap) {
        if (reportMap.isEmpty()) {
            return;
        }
        
        try {
            long startTime = System.currentTimeMillis();
            logger.info("Starting batch save of {} reports", reportMap.size());
            
            // Convert map to list
            List<VastReport> reportsToSave = new ArrayList<>(reportMap.values());
            
            // Use the batch save method
            vastReportRepository.batchSave(reportsToSave);
            
            long duration = System.currentTimeMillis() - startTime;
            logger.info("Completed batch save of {} reports in {} ms", reportMap.size(), duration);
        } catch (Exception e) {
            logger.error("Error in batch save of reports: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Load ad start timestamps from Redis to in-memory cache
     */
    private void loadAdStartTimestampsFromRedis() {
        Map<String, String> redisData = redisService.getAdStartTimestamps();
        
        for (Map.Entry<String, String> entry : redisData.entrySet()) {
            String adId = entry.getKey();
            try {
                Long timestamp = Long.parseLong(entry.getValue());
                adStartTimestampCache.put(adId, timestamp);
            } catch (NumberFormatException e) {
                logger.warn("Invalid timestamp format in Redis for adId {}: {}", adId, entry.getValue());
            }
        }
    }
    
    /**
     * Save ad start timestamps from in-memory cache to Redis
     */
    private void saveAdStartTimestampsToRedis() {
        if (adStartTimestampCache.isEmpty()) {
            return;
        }
        
        // Tạo bản sao để tránh ConcurrentModificationException
        Map<String, Long> timestampsToSave = new HashMap<>(adStartTimestampCache);
        redisService.saveAdStartTimestamps(timestampsToSave);
        logger.info("Saved {} ad start timestamps to Redis", timestampsToSave.size());
    }
}
