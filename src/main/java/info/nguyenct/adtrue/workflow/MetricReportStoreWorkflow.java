package info.nguyenct.adtrue.workflow;

import info.nguyenct.adtrue.model.AdsWorkflowMetricRecord;
import info.nguyenct.adtrue.model.SspDevice;
import info.nguyenct.adtrue.ssp.vistar.VistarMetricRecord;
import info.nguyenct.adtrue.repository.AdsWorkflowMetricRepository;
import info.nguyenct.adtrue.repository.SspDeviceMetricRepository;
import info.nguyenct.adtrue.repository.VistarMetricRepository;
import info.nguyenct.adtrue.service.RedisService;
import info.nguyenct.adtrue.service.SspDeviceManager;
import info.nguyenct.adtrue.ssp.vistar.VistarSsp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class MetricReportStoreWorkflow {
    private static final Logger logger = LoggerFactory.getLogger(MetricReportStoreWorkflow.class);

    private final SspDeviceManager sspDeviceManager;
    private final SspDeviceMetricRepository sspDeviceMetricRepository;
    private final VistarMetricRepository vistarMetricRepository;
    private final VistarSsp vistarSsp;
    private final RedisService redisService;
    private final AdsGettingWorkflow adsGettingWorkflow;
    private final AdsWorkflowMetricRepository adsWorkflowMetricRepository;
    
    // Lưu giá trị counter từ lần chạy trước
    private Map<String, Integer> previousCounters = new ConcurrentHashMap<>();

    @Autowired
    public MetricReportStoreWorkflow(SspDeviceManager sspDeviceManager, 
                                    SspDeviceMetricRepository sspDeviceMetricRepository,
                                    VistarMetricRepository vistarMetricRepository,
                                    VistarSsp vistarSsp,
                                    RedisService redisService,
                                    AdsGettingWorkflow adsGettingWorkflow,
                                    AdsWorkflowMetricRepository adsWorkflowMetricRepository) {
        this.sspDeviceManager = sspDeviceManager;
        this.sspDeviceMetricRepository = sspDeviceMetricRepository;
        this.vistarMetricRepository = vistarMetricRepository;
        this.vistarSsp = vistarSsp;
        this.redisService = redisService;
        this.adsGettingWorkflow = adsGettingWorkflow;
        this.adsWorkflowMetricRepository = adsWorkflowMetricRepository;
    }



    @Scheduled(cron = "0 */5 * * * *") // Run every minute (60,000 ms)
    public void storeDeviceMetrics() {
        logger.info("Storing SSP device metrics to database");
        List<SspDevice> devices = sspDeviceManager.getAllDevices();
        LocalDateTime collectedAt = LocalDateTime.now();
        
        // Use batch save instead of individual saves
        sspDeviceMetricRepository.batchSave(devices, collectedAt);
        
        logger.info("Completed storing metrics for {} devices", devices.size());
    }

    /**
     * Store Vistar SSP metrics to database every 5 minutes
     * Stores delta values for counter metrics
     */
    @Scheduled(cron = "0 */1 * * * *") // Run every 5 minutes
    public void storeVistarMetrics() {
        logger.info("Storing Vistar SSP metrics to database");

        // Get metrics directly from VistarSsp
        Map<String, Integer> currentMetrics = vistarSsp.getMetrics();
        
        // Calculate delta values for counter metrics
        Map<String, Integer> deltaMetrics = new HashMap<>(currentMetrics);
        for (String key : currentMetrics.keySet()) {
            // Only calculate delta for counter metrics, not gauge metrics
            if (isCounterMetric(key)) {
                int currentValue = currentMetrics.get(key);
                int previousValue = previousCounters.getOrDefault("vistar:" + key, 0);
                int delta = currentValue - previousValue;
                // Ensure delta is non-negative (in case of counter reset)
                deltaMetrics.put(key, Math.max(0, delta));
            }
        }
        
        // Create metric record with delta values
        VistarMetricRecord metricRecord = new VistarMetricRecord(deltaMetrics);
        
        try {
            // Save to database
            vistarMetricRepository.save(metricRecord);
            
            // Update previous counters after successful save
            for (String key : currentMetrics.keySet()) {
                if (isCounterMetric(key)) {
                    previousCounters.put("vistar:" + key, currentMetrics.get(key));
                }
            }
            
            logger.info("Completed storing Vistar SSP metrics to database");
        } catch (Exception e) {
            logger.error("Failed to store Vistar SSP metrics: {}", e.getMessage(), e);
        }
    }

    /**
     * Store AdsGettingWorkflow metrics to database every 5 minutes
     * Stores delta values for counter metrics
     */
    @Scheduled(cron = "0 */1 * * * *") // Run every 5 minutes
    public void storeAdsWorkflowMetrics()   {
        logger.info("Storing AdsGettingWorkflow metrics to database");
        
        // Get metrics from AdsGettingWorkflow
        Map<String, Integer> currentMetrics = adsGettingWorkflow.getMetrics();
        
        // Calculate delta values for counter metrics
        Map<String, Integer> deltaMetrics = new HashMap<>(currentMetrics);
        for (String key : currentMetrics.keySet()) {
            // Only calculate delta for counter metrics, not gauge metrics
            if (isCounterMetric(key)) {
                int currentValue = currentMetrics.get(key);
                int previousValue = previousCounters.getOrDefault("workflow:" + key, 0);
                int delta = currentValue - previousValue;
                // Ensure delta is non-negative (in case of counter reset)
                deltaMetrics.put(key, Math.max(0, delta));
            }
        }
        
        // Create metric record with delta values
        AdsWorkflowMetricRecord metricRecord = new AdsWorkflowMetricRecord(deltaMetrics);
        
        try {
            // Save to database
            adsWorkflowMetricRepository.save(metricRecord);
            
            // Update previous counters after successful save
            for (String key : currentMetrics.keySet()) {
                if (isCounterMetric(key)) {
                    previousCounters.put("workflow:" + key, currentMetrics.get(key));
                }
            }
            
            logger.info("Completed storing AdsGettingWorkflow metrics to database");
        } catch (Exception e) {
            logger.error("Failed to store AdsGettingWorkflow metrics: {}", e.getMessage(), e);
        }
    }

    /**
     * Determine if a metric is a counter (accumulating) or gauge (point-in-time) metric
     * @param metricName Name of the metric
     * @return true if the metric is a counter, false if it's a gauge
     */
    private boolean isCounterMetric(String metricName) {
        // List of gauge metrics (point-in-time values that shouldn't use deltas)
        return !metricName.equals("pending_proof_of_play_calls") && 
               !metricName.equals("device_count") &&
               !metricName.equals("cache_size") &&
               !metricName.contains("duration_p95");
    }
}
