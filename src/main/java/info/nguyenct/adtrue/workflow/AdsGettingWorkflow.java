package info.nguyenct.adtrue.workflow;

import info.nguyenct.adtrue.model.AdtrueAd;
import info.nguyenct.adtrue.model.SspDevice;
import info.nguyenct.adtrue.service.RedisService;
import info.nguyenct.adtrue.ssp.AdtrueSsp;
import info.nguyenct.adtrue.ssp.Ssp;
import info.nguyenct.adtrue.ssp.cm.CMSsp;
import info.nguyenct.adtrue.ssp.lmx.LmxSsp;
import info.nguyenct.adtrue.ssp.vistar.VistarSsp;
import info.nguyenct.adtrue.ssp.modcart.ModcartSsp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.Counter;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class AdsGettingWorkflow {
    private static final Logger logger = LoggerFactory.getLogger(AdsGettingWorkflow.class);

    private final CMSsp cmSsp;
    private final VistarSsp vistarSsp;
    private final AdtrueSsp adtrueSsp;
    private final LmxSsp lmxSsp;
    private final ModcartSsp modcartSsp;
    private final RedisService redisService;
    
    // Replace Counter with AtomicInteger
    private final AtomicInteger adsRequestCounter = new AtomicInteger(0);
    private final AtomicInteger cmAdsResponseCounter = new AtomicInteger(0);
    private final AtomicInteger vistarAdsResponseCounter = new AtomicInteger(0);
    private final AtomicInteger lmxAdsResponseCounter = new AtomicInteger(0);
    private final AtomicInteger modcartAdsResponseCounter = new AtomicInteger(0);
    private final AtomicInteger adtrueAdsResponseCounter = new AtomicInteger(0);

    // Timer metrics
    private final Timer cmAdsRequestTimer;
    private final Timer vistarAdsRequestTimer;
    private final Timer lmxAdsRequestTimer;
    private final Timer modcartAdsRequestTimer;

    // Replace single variables with maps to track per device
    private final Map<String, LocalDateTime> lastLmxCallTimeMap = new HashMap<>();
    private final Map<String, LocalDateTime> lastModcartCallTimeMap = new HashMap<>();

    // List of devices that can receive LMX ads
    private static final List<String> LMX_ENABLED_DEVICES = List.of(
        "ATT-OFC-FL2-TEZ-59Q0D1", 
        "ATT-OFC-FL2-TEZ-59Q0D2",
        "ATT-OFC-FL2-TEZ-59Q0C1",
        "ATT-OFC-FL2-TEZ-59Q0D3",
        "ATT-OFC-FL2-TEZ-59Q0C2",
        "ATT-OFC-FL2-TEZ-59Q0D4",
        "ATT-OFC-FL2-TEZ-59Q0D5",
        "ATT-OFC-FL2-TEZ-59Q0D7",
        "ATT-OFC-FL2-TEZ-59Q0D8",
        "ATT-OFC-FL2-TEZ-59Q0D9"
    );

    public AdsGettingWorkflow(CMSsp cmSsp, VistarSsp vistarSsp, AdtrueSsp adtrueSsp, LmxSsp lmxSsp,
                             ModcartSsp modcartSsp, RedisService redisService, MeterRegistry meterRegistry) {
        this.cmSsp = cmSsp;
        this.vistarSsp = vistarSsp;
        this.adtrueSsp = adtrueSsp;
        this.lmxSsp = lmxSsp;
        this.modcartSsp = modcartSsp;
        this.redisService = redisService;
        
        // Initialize timer metrics
        this.cmAdsRequestTimer = Timer.builder("cm.ads.request.duration")
                .description("Time taken for CM ads requests")
                .distributionStatisticExpiry(Duration.ofMinutes(5))
                .publishPercentileHistogram(true)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register(meterRegistry);
        
        this.vistarAdsRequestTimer = Timer.builder("vistar.ads.workflow.duration")
                .description("Time taken for Vistar ads requests in workflow")
                .distributionStatisticExpiry(Duration.ofMinutes(5))
                .publishPercentileHistogram(true)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register(meterRegistry);

        this.lmxAdsRequestTimer = Timer.builder("lmx.ads.workflow.duration")
                .description("Time taken for Lmx ads requests in workflow")
                .distributionStatisticExpiry(Duration.ofMinutes(5))
                .publishPercentileHistogram(true)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register(meterRegistry);
        
        this.modcartAdsRequestTimer = Timer.builder("modcart.ads.request.workflow.time")
                .description("Time taken to request ads from Modcart in workflow")
                .distributionStatisticExpiry(Duration.ofMinutes(5))
                .publishPercentileHistogram(true)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register(meterRegistry);
    }

    /**
     * Get ad for device with optional SSP filter
     * @param device Device to get ad for
     * @param requestedTimestamp Timestamp of request
     * @param ssp Optional SSP to filter by (vistar, cm, lmx, modcart, adtrue)
     * @return AdtrueAd object or null if no ad found
     */
    public AdtrueAd getAdForDevice(SspDevice device, long requestedTimestamp, String ssp) {
        String deviceId = device.getDevice_id();
        
        // Check cache first
        AdtrueAd cachedAd = getFromCache(deviceId);
        if (cachedAd != null) {
            logger.info("Using cached ad for device: {}", deviceId);
            return cachedAd;
        }
        
        // Increment request counter
        adsRequestCounter.incrementAndGet();
        LocalDateTime now = LocalDateTime.now();

        // Check if specific SSP was requested
        if (ssp != null && !ssp.isEmpty()) {
            logger.info("Requesting ad from specific SSP: {} for device: {}", ssp, deviceId);

            switch (ssp.toLowerCase()) {
                case "vistar":
                    return getAdFromVistar(device, requestedTimestamp);
                case "cm":
                    return getAdFromCm(device, requestedTimestamp);
                case "lmx":
                    return getAdFromLmx(device, requestedTimestamp);
                case "modcart":
                    return getAdFromModcart(device, requestedTimestamp);
                case "adtrue":
                    return getAdFromAdtrue(device, requestedTimestamp);
                default:
                    logger.warn("Unknown SSP: {}, falling back to normal flow", ssp);
                    break;
            }
        }
        
        // Check if device is in LMX enabled list
        if (LMX_ENABLED_DEVICES.contains(deviceId)) {
            // Check if we've already called LMX for this device in this minute
            LocalDateTime lastLmxCall = lastLmxCallTimeMap.get(deviceId);
            if (now.getMinute() % 2 == 0 && (lastLmxCall == null || lastLmxCall.getMinute() != now.getMinute())) {
                AdtrueAd lmAd = getAdFromLmx(device, requestedTimestamp);
                lastLmxCallTimeMap.put(device.getDevice_id(), LocalDateTime.now());
                if (lmAd != null) return lmAd;
            }

            LocalDateTime lastModcartCall = lastModcartCallTimeMap.get(deviceId);
            if (now.getMinute() % 2 == 1 && (lastModcartCall == null || lastModcartCall.getMinute() != now.getMinute())) {
                AdtrueAd modcartAd = getAdFromModcart(device, requestedTimestamp);
                lastModcartCallTimeMap.put(device.getDevice_id(), LocalDateTime.now());
                if (modcartAd != null) return modcartAd;
            }
        }

        // Try CM first
        AdtrueAd cmAd = getAdFromCm(device, requestedTimestamp);
        if (cmAd != null) {
            return cmAd;
        }

        logger.info("No ads found for device {} from CM, checking Vistar", deviceId);

        // Then try Vistar
        AdtrueAd vistarAd = getAdFromVistar(device, requestedTimestamp);
        if (vistarAd != null) {
            return vistarAd;
        }
        
        logger.info("No ads found for device {} from Vistar, checking Adtrue", deviceId);

        // Finally, fall back to Adtrue
        return getAdFromAdtrue(device, requestedTimestamp);
    }

    private AdtrueAd getAdFromLmx(SspDevice device, long requestedTimestamp) {
        long start = System.currentTimeMillis();
        AdtrueAd lmAd = lmxSsp.requestAd(device, requestedTimestamp);
        lmxAdsRequestTimer.record(System.currentTimeMillis() - start, TimeUnit.MILLISECONDS);
        if (lmAd != null) {
            lmxAdsResponseCounter.incrementAndGet();
            addToCache(device.getDevice_id(), lmAd, 15);
            return lmAd;
        }
        return null;
    }

    private AdtrueAd getAdFromVistar(SspDevice device, long requestedTimestamp) {
        if (device.getVenue_id() == null || device.getVenue_id().isEmpty()) {
            logger.info("Skipping Vistar for device {} because venue_id is null or empty", device.getDevice_id());
            return null;
        }
        
        long start = System.currentTimeMillis();
        AdtrueAd vistarAd = vistarSsp.requestAd(device, requestedTimestamp);
        vistarAdsRequestTimer.record(System.currentTimeMillis() - start, TimeUnit.MILLISECONDS);
        
        if (vistarAd != null) {
            vistarAdsResponseCounter.incrementAndGet();
            addToCache(device.getDevice_id(), vistarAd, 15);
            return vistarAd;
        }
        return null;
    }

    private AdtrueAd getAdFromCm(SspDevice device, long requestedTimestamp) {
        long start = System.currentTimeMillis();
        AdtrueAd cmAd = cmSsp.requestAd(device, requestedTimestamp);
        cmAdsRequestTimer.record(System.currentTimeMillis() - start, TimeUnit.MILLISECONDS);
        
        if (cmAd != null) {
            cmAdsResponseCounter.incrementAndGet();
            addToCache(device.getDevice_id(), cmAd, 15);
            return cmAd;
        }
        return null;
    }

    private AdtrueAd getAdFromModcart(SspDevice device, long requestedTimestamp) {
        long start = System.currentTimeMillis();
        AdtrueAd modcartAd = modcartSsp.requestAd(device, requestedTimestamp);
        modcartAdsRequestTimer.record(System.currentTimeMillis() - start, TimeUnit.MILLISECONDS);

        if (modcartAd != null) {
            modcartAdsResponseCounter.incrementAndGet();
            addToCache(device.getDevice_id(), modcartAd, 15);
            return modcartAd;
        }
        return null;
    }

    private AdtrueAd getAdFromAdtrue(SspDevice device, long requestedTimestamp) {
        AdtrueAd adtrueAd = adtrueSsp.requestAd(device, requestedTimestamp);
        if (adtrueAd != null) {
            adtrueAdsResponseCounter.incrementAndGet();
            addToCache(device.getDevice_id(), adtrueAd, 15);
        }
        return adtrueAd;
    }

    /**
     * Get current metrics as a map
     * @return Map of metric names to values
     */
    public Map<String, Integer> getMetrics() {
        Map<String, Integer> metrics = new HashMap<>();
        
        // Add counter metrics
        metrics.put("ads_requests", adsRequestCounter.get());
        metrics.put("cm_ads_responses", cmAdsResponseCounter.get());
        metrics.put("vistar_ads_responses", vistarAdsResponseCounter.get());
        metrics.put("adtrue_ads_responses", adtrueAdsResponseCounter.get());
        metrics.put("lmx_ads_responses", lmxAdsResponseCounter.get());
        metrics.put("modcart_ads_responses", modcartAdsResponseCounter.get());
        
        // Add cache metrics
        metrics.put("cache_size", getAdCacheSize());
        
        // Add timer metrics (95th percentile in milliseconds, rounded to integer)
        try {
            if (cmAdsRequestTimer.takeSnapshot().percentileValues().length > 1) {
                metrics.put("cm_ads_request_duration_p95", 
                    (int) Math.round(cmAdsRequestTimer.takeSnapshot().percentileValues()[1].value(TimeUnit.MILLISECONDS)));
            } else {
                metrics.put("cm_ads_request_duration_p95", -1);
            }
            
            if (vistarAdsRequestTimer.takeSnapshot().percentileValues().length > 1) {
                metrics.put("vistar_ads_request_duration_p95", 
                    (int) Math.round(vistarAdsRequestTimer.takeSnapshot().percentileValues()[1].value(TimeUnit.MILLISECONDS)));
            } else {
                metrics.put("vistar_ads_request_duration_p95", -1);
            }

            if (lmxAdsRequestTimer.takeSnapshot().percentileValues().length > 1) {
                metrics.put("lmx_ads_request_duration_p95",
                    (int) Math.round(lmxAdsRequestTimer.takeSnapshot().percentileValues()[1].value(TimeUnit.MILLISECONDS)));
            } else {
                metrics.put("lmx_ads_request_duration_p95", -1);
            }

            if (modcartAdsRequestTimer.takeSnapshot().percentileValues().length > 1) {
                metrics.put("modcart_ads_request_duration_p95",
                        (int) Math.round(modcartAdsRequestTimer.takeSnapshot().percentileValues()[1].value(TimeUnit.MILLISECONDS)));
            } else {
                metrics.put("modcart_ads_request_duration_p95", -1);
            }
        } catch (Exception e) {
            logger.warn("Error calculating timer metrics: {}", e.getMessage());
            // Set default values if there's an error
            metrics.put("cm_ads_request_duration_p95", 0);
            metrics.put("vistar_ads_request_duration_p95", 0);
            metrics.put("lmx_ads_request_duration_p95", 0);
            metrics.put("modcart_ads_request_duration_p95", 0);
        }

        return metrics;
    }


    private AdtrueAd getFromCache(String deviceId) {
        return redisService.getCachedAd(deviceId);
    }

    private void addToCache(String deviceId, AdtrueAd ad, int minutes) {
        logger.info("Caching ad for device {} adId {}", deviceId, ad.getAdId());
        redisService.cacheAd(deviceId, ad, minutes);
    }

    public void removeAdFromCache(String deviceId, String adId) {
        AdtrueAd ad = redisService.getCachedAd(deviceId);
        if (ad != null && ad.getAdId().equals(adId)) {
            logger.info("Removing ad {} from cache for device {}", adId, deviceId);
            redisService.removeAdFromCache(deviceId);
        }
    }

    public int getAdCacheSize() {
        return redisService.getAdCacheSizeBySsp();
    }
}
