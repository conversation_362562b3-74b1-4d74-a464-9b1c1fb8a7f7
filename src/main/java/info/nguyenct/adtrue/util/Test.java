package info.nguyenct.adtrue.util;

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Path;

public class Test {
    public static void main(String[] args) throws MalformedURLException {
        String path = "https://ott.trueview.com.vn/videos/viettel/tvc/video.mp4";
        URL url = new URL(path);
        System.out.println(url.getPath() + "\n" + url.getFile());

        Path p = Path.of("C:\\Project\\AdTrueServer\\src\\main\\resources\\application-dev.properties");
        String filename = p.getFileName().toString();
        String filenameWithoutExtension = FileUtil.getFilenameWithoutExtension(p);
        String extension = FileUtil.getExtensionFromPath(p);
        System.out.println("Filename: " + filename +
                "\nFilename without extension: " + filenameWithoutExtension +
                "\nExtension: " + extension);

        Path newPath = p.getParent().resolve("newfile.txt");
        System.out.println("New Path: " + newPath.toAbsolutePath().toString());

        String f = "abc.text.fsadas.txt";
        System.out.println("Filename without extension: " + FileUtil.getFilenameWithoutExtension(f));
    }
}
