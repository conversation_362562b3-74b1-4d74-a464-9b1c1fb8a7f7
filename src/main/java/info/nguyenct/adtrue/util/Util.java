package info.nguyenct.adtrue.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import info.nguyenct.adtrue.model.AdtrueAd;
import info.nguyenct.adtrue.model.VastEventType;
import info.nguyenct.adtrue.model.VastLogRequest;
import jakarta.servlet.http.HttpServletRequest;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

public class Util
{
    public static long getTimeStamp(LocalDateTime time) {
        if (time == null) {
            return 0;
        }
        return time.atZone(ZoneId.systemDefault()).toEpochSecond();
    }

    public static long getTimeStampNow() {
        return LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond();
    }

    public static long getTimeStampUTCNow() {
        return LocalDateTime.now().atZone(ZoneId.of("UTC")).toEpochSecond();
    }

    public static VastLogRequest fromLogString(String logString) {
        VastLogRequest logRequest = new VastLogRequest();
        String[] parts = logString.split("\\^");
        if (parts.length < 17) {
            return null;
        }

        logRequest.setCampaignId(parts[0]);
        logRequest.setAdId(parts[1]);
        logRequest.setEvent(VastEventType.fromCode(parts[10]));
        logRequest.setIp(parts[12]);
        logRequest.setMac(parts[13]);
        logRequest.setDeviceId(parts[14]);

        try {
            logRequest.setChannelId(Integer.parseInt(parts[2]));
            logRequest.setVendorId(Integer.parseInt(parts[3]));
            logRequest.setRegionId(Integer.parseInt(parts[4]));
            logRequest.setCityId(Integer.parseInt(parts[5]));
            logRequest.setDistrictId(Integer.parseInt(parts[6]));
            logRequest.setWardId(Integer.parseInt(parts[7]));
            logRequest.setStoreId(Integer.parseInt(parts[8]));
            logRequest.setMultiplier(Integer.parseInt(parts[9]));
            logRequest.setDuration(Integer.parseInt(parts[11]));
            logRequest.setTimestamp(Long.parseLong(parts[15]));
            logRequest.setEventTime(Long.parseLong(parts[16]));
        } catch (NumberFormatException e) {
            return null;
        }

        return logRequest;
    }

    public static String toLogString(VastLogRequest logRequest) {
        return new StringBuilder()
                .append(logRequest.getCampaignId()).append("^")
                .append(logRequest.getAdId()).append("^")
                .append(logRequest.getChannelId()).append("^")
                .append(logRequest.getVendorId()).append("^")
                .append(logRequest.getRegionId()).append("^")
                .append(logRequest.getCityId()).append("^")
                .append(logRequest.getDistrictId()).append("^")
                .append(logRequest.getWardId()).append("^")
                .append(logRequest.getStoreId()).append("^")
                .append(logRequest.getMultiplier()).append("^")
                .append(logRequest.getEvent().getValue()).append("^")
                .append(logRequest.getDuration()).append("^")
                .append(logRequest.getIp()).append("^")
                .append(logRequest.getMac()).append("^")
                .append(logRequest.getDeviceId())
                .toString();
    }

    public static int getRandomInt(int min, int max) {
        return min + (int)(Math.random() * ((max - min) + 1));
    }

    public static int getRandomInt(int max) {
        return (int)(Math.random() * max);
    }

    public static int getHourMinute(String time) {
        return Integer.parseInt(time.split(":")[0]) * 60 + Integer.parseInt(time.split(":")[1]);
    }

    public static int getHourMinuteNow() {
        LocalDateTime now = LocalDateTime.now();
        return  now.getHour() * 60 + now.getMinute();
    }

    public static int getMonthDayNow() {
        LocalDate now = LocalDate.now();
        return now.getMonthValue()*100 + now.getDayOfMonth();
    }

    public static String getBaseUrlFromRequest(HttpServletRequest request) {
        String scheme = request.getScheme();
        String serverName = request.getServerName();
        int serverPort = request.getServerPort();

        // Xây dựng base URL
        StringBuilder url = new StringBuilder();
        url.append(scheme).append("://").append(serverName);

        // Thêm port nếu không phải port mặc định
        if ((scheme.equals("http") && serverPort != 80) ||
                (scheme.equals("https") && serverPort != 443)) {
            url.append(":").append(serverPort);
        }

        return url.toString();
    }

    public static class MyJsonDeserialize<T> implements Function<String, T> {
        private final Class<T> clazz;
        private final ObjectMapper objectMapper;

        public MyJsonDeserialize(ObjectMapper objectMapper, Class<T> clazz) {
            this.clazz = clazz;
            this.objectMapper = objectMapper;
        }

        @Override
        public T apply(String s) {
            try {
                return objectMapper.readValue(s, clazz);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        }
    }

    public static String getVastString(String baseDataFormat, AdtrueAd ad, String assetUrl) {
        String impressionUrl = String.format(baseDataFormat, "1");
        String creativeViewUrl = String.format(baseDataFormat, "2");
        String startUrl = String.format(baseDataFormat, "3");
        String firstQuartileUrl = String.format(baseDataFormat, "4");
        String midpointUrl = String.format(baseDataFormat, "5");
        String thirdQuartileUrl = String.format(baseDataFormat, "6");
        String completeUrl = String.format(baseDataFormat, "7");
        String clickThroughUrl = String.format(baseDataFormat, "8");
        String errorUrl = String.format(baseDataFormat, "9");

        // Format duration as HH:MM:SS
        String lengthStr = ad.getLengthInSeconds();
        int seconds = (lengthStr != null && !lengthStr.equals("null")) ? Integer.parseInt(lengthStr) : 15;
        String duration = String.format("%02d:%02d:%02d", seconds / 3600, (seconds % 3600) / 60, seconds % 60);

        // Create VAST XML using StringBuilder with proper indentation
        StringBuilder vastXmlBuilder = new StringBuilder();
        vastXmlBuilder.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n")
                .append("<VAST xmlns:xsi=\"https://www.w3.org/2001/XMLSchema-instance\" version=\"2.0\" xsi:noNamespaceSchemaLocation=\"vast.xsd\">\n")
                .append("  <Ad id=\"").append(ad.getAdId()).append("\">\n")
                .append("    <InLine>\n")
                .append("      <AdSystem>AdTRUE</AdSystem>\n")
                .append("      <AdTitle>In-Stream Video</AdTitle>\n")
                .append("      <Error>\n")
                .append("        <![CDATA[").append(errorUrl).append("]]>\n")
                .append("      </Error>\n")
                .append("      <Impression>\n")
                .append("        <![CDATA[").append(impressionUrl).append("]]>\n")
                .append("      </Impression>\n")
                .append("      <Creatives>\n")
                .append("        <Creative AdID=\"").append(ad.getAdId()).append("\">\n")
                .append("          <Linear>\n")
                .append("            <Duration>").append(duration).append("</Duration>\n")
                .append("            <TrackingEvents>\n")
                .append("              <Tracking event=\"creativeView\">\n")
                .append("                <![CDATA[").append(creativeViewUrl).append("]]>\n")
                .append("              </Tracking>\n")
                .append("              <Tracking event=\"start\">\n")
                .append("                <![CDATA[").append(startUrl).append("]]>\n")
                .append("              </Tracking>\n")
                .append("              <Tracking event=\"midpoint\">\n")
                .append("                <![CDATA[").append(midpointUrl).append("]]>\n")
                .append("              </Tracking>\n")
                .append("              <Tracking event=\"firstQuartile\">\n")
                .append("                <![CDATA[").append(firstQuartileUrl).append("]]>\n")
                .append("              </Tracking>\n")
                .append("              <Tracking event=\"thirdQuartile\">\n")
                .append("                <![CDATA[").append(thirdQuartileUrl).append("]]>\n")
                .append("              </Tracking>\n")
                .append("              <Tracking event=\"complete\">\n")
                .append("                <![CDATA[").append(completeUrl).append("]]>\n")
                .append("              </Tracking>\n")
                .append("            </TrackingEvents>\n")
                .append("            <MediaFiles>\n")
                .append("              <MediaFile delivery=\"progressive\" type=\"").append(ad.getMimeType())
                .append("\" bitrate=\"").append(ad.getBitrate())
                .append("\" width=\"").append(ad.getWidth())
                .append("\" height=\"").append(ad.getHeight())
                .append("\" scalable=\"true\" maintainAspectRatio=\"true\">\n")
                .append("                <![CDATA[").append(assetUrl).append("]]>\n")
                .append("              </MediaFile>\n")
                .append("            </MediaFiles>\n")
                .append("            <VideoClicks>\n")
                .append("              <ClickThrough>\n")
                .append("                <![CDATA[").append(clickThroughUrl).append("]]>\n")
                .append("              </ClickThrough>\n")
                .append("            </VideoClicks>\n")
                .append("          </Linear>\n")
                .append("        </Creative>\n")
                .append("        <Creative AdID=\"").append(ad.getAdId()).append("\"/>\n")
                .append("      </Creatives>\n")
                .append("    </InLine>\n")
                .append("  </Ad>\n")
                .append("</VAST>");

        return vastXmlBuilder.toString();
    }

    public static int parseInt(String value, int defaultValue) {
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    public static void main(String[] args) {
        int count = 0;
        Random random = new Random();
        for (int i = 0; i < 1_000_000; i++) {
            if (random.nextFloat(1) < random.nextFloat(0.1f)) {
                count++;
            }
        }
        System.out.println(count);
    }
}

