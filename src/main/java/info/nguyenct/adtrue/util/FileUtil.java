package info.nguyenct.adtrue.util;

import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Path;
import java.util.regex.Matcher;

public class FileUtil {
    public static String getExtensionFromUrl(URL url) throws MalformedURLException {
        return getExtensionFromFilename(getFilenameFromUrl(url));
    }

    public static String getExtensionFromUrl(String url) throws MalformedURLException {
        return getExtensionFromFilename(getFilenameFromUrl(url));
    }

    public static String getFilenameFromUrl(String url) throws MalformedURLException {
        return getFilenameFromUrl(new URL(url));
    }

    public static String getFilenameFromUrl(URL url) {
        try {
            String path = url.getPath();
            int slashIndex = path.lastIndexOf('/');
            if (slashIndex < 0 || slashIndex >= path.length() - 1) {
                return null; // Không có tên file hợp lệ
            }
            return path.substring(path.lastIndexOf('/') + 1).toLowerCase();
        } catch (Exception e) {
            return null;
        }
    }

    public static String getExtensionFromPath(Path path) {
        String filename = path.getFileName().toString();
        return getExtensionFromFilename(filename);
    }

    public static String getFilenameWithoutExtension(Path path) {
        String filename = path.getFileName().toString();
        return getFilenameWithoutExtension(filename);
    }

    public static String getExtensionFromFilename(String filename) {
        if (filename == null || filename.isEmpty()) {
            return null;
        }
        int dotIndex = filename.lastIndexOf('.');
        if (dotIndex > 0) {
            return filename.substring(dotIndex).toLowerCase();
        }
        return null;
    }

    public static String getFilenameWithoutExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return null;
        }
        int dotIndex = filename.lastIndexOf('.');
        if (dotIndex > 0) {
            return filename.substring(0, dotIndex).toLowerCase();
        }
        return filename.toLowerCase();
    }
}
