package info.nguyenct.adtrue.util;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.cumulative.CumulativeTimer;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.sql.SQLOutput;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

public class TestMetric
{
    public static void main(String[] args) throws InterruptedException {
        Class clazz = String.class;
        System.out.println(clazz == String.class);
        MeterRegistry meterRegistry = new SimpleMeterRegistry();
        Timer timer = Timer.builder("test")
                .distributionStatisticExpiry(java.time.Duration.ofMinutes(1))
                .publishPercentileHistogram(true)
                .publishPercentiles(0.5, 0.95, 0.99)
                .register( meterRegistry);

        Counter counter = Counter.builder("test.counter")
                .description("Test counter")
                .register(meterRegistry);

        Counter incrementCounter = Counter.builder("test.increment")
                .description("Test increment counter")
                .register(meterRegistry);
        CountDownLatch countDownLatch = new CountDownLatch(1);
        for (int i = 0; i < 32; i++) {
            TestRunner runner = new TestRunner(countDownLatch, timer, counter, incrementCounter);
            runner.start();
        }
        clazz = CumulativeTimer.class;
        System.out.println(timer.getClass().toString() + " " + (timer.getClass() == clazz));
        countDownLatch.countDown();
        Thread.sleep(10000);
        System.out.println("Done");
        System.out.println(counter.count());
        System.out.println(incrementCounter.count());
        System.out.println(timer.count());
        System.out.println(timer.max(TimeUnit.MILLISECONDS));
        System.out.println(timer.percentile(0.5, TimeUnit.MILLISECONDS));
        System.out.println(timer.percentile(0.95, TimeUnit.MILLISECONDS));
        System.out.println(timer.percentile(0.99, TimeUnit.MILLISECONDS));

        Thread.sleep(100000);
        System.out.println(timer.count());
        System.out.println(timer.max(TimeUnit.MILLISECONDS));
        System.out.println(timer.percentile(0.5, TimeUnit.MILLISECONDS));
        System.out.println(timer.percentile(0.95, TimeUnit.MILLISECONDS));
        System.out.println(timer.percentile(0.99, TimeUnit.MILLISECONDS));


    }

    public static class TestRunner extends Thread {
        private CountDownLatch countDownLatch;
        private Timer timer;
        private Counter counter;
        private Counter incrementCounter;

        public TestRunner(CountDownLatch countDownLatch, Timer timer, Counter counter, Counter incrementCounter) {
            this.countDownLatch = countDownLatch;
            this.timer = timer;
            this.counter = counter;
            this.incrementCounter = incrementCounter;
        }

        @Override
        public void run() {
            try {
                countDownLatch.await();
                System.out.println(Thread.currentThread().getName() + " Starting");
                for (int i = 1; i < 1000; i++) {
                    timer.record(i, java.util.concurrent.TimeUnit.MILLISECONDS);
                    incrementCounter.increment();
                    counter.increment(i);
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

        }
    }
}
