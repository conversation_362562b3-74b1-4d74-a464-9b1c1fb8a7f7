package info.nguyenct.adtrue.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import info.nguyenct.adtrue.model.AdsHourBlock;
import info.nguyenct.adtrue.model.DisplayArea;
import info.nguyenct.adtrue.model.HourlyMultiplier;
import info.nguyenct.adtrue.model.SspDevice;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * CSV importer cho SspDevice, đọc header và map cột -> field tương ứng.
 * - Các trường đơn giản: device_id, mac, venue_id, display_time, latitude, longitude,
 *   ads_duration_hourly, ads_file_type, channel_id, vendor_id, region_id, city_id, district_id,
 *   ward_id, store_id, lmx_url, modcart_url, cacheCreatives, lastRequestTime.
 * - Các trường list/nested: display_area, ads_hour_block, multiplier (dưới dạng JSON).
 * - Fallback multiplier:
 *     + multiplier_value: áp 1 giá trị cho tất cả 7 ngày x 24h.
 *     + mon, tue, wed, thu, fri, sat, sun: nếu có, áp cho 7 ngày x 24h.
 */
public class SspDeviceCsvImporter {

    public static List<SspDevice> processCsvFile(String filePath) throws IOException {
        List<SspDevice> devices = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();

        InputStream is = SspDeviceCsvImporter.class.getClassLoader().getResourceAsStream(filePath);
        if (is == null) {
            throw new IOException("Cannot find CSV resource on classpath: " + filePath);
        }

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
            // Đọc header
            String headerLine;
            do {
                headerLine = reader.readLine();
            } while (headerLine != null && headerLine.trim().isEmpty());

            if (headerLine == null) {
                return devices;
            }

            List<String> headers = parseCsvLine(headerLine);
            Map<String, Integer> idx = new HashMap<>();
            for (int i = 0; i < headers.size(); i++) {
                idx.put(headers.get(i).trim().toLowerCase(Locale.ROOT), i);
            }

            // Đọc từng dòng dữ liệu
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.trim().isEmpty()) continue;

                List<String> cols = parseCsvLine(line);

                // Yêu cầu có device_id
                String deviceId = getString(idx, cols, "device_id");
                if (deviceId == null || deviceId.isEmpty()) {
                    // Bỏ qua dòng không có device_id
                    continue;
                }

                SspDevice device = new SspDevice(deviceId);

                // Map các trường đơn giản
                setIfPresent(idx, cols, "mac", v -> device.setMac(v));
                setIfPresent(idx, cols, "venue_id", v -> device.setVenue_id(v));
                setIfPresentLong(idx, cols, "display_time", device::setDisplay_time);
                setIfPresentDouble(idx, cols, "latitude", device::setLatitude);
                setIfPresentDouble(idx, cols, "longitude", device::setLongitude);
                setIfPresentInt(idx, cols, "ads_duration_hourly", device::setAds_duration_hourly);
                setIfPresent(idx, cols, "ads_file_type", device::setAds_file_type);

                setIfPresentInt(idx, cols, "channel_id", device::setChannel_id);
                setIfPresentInt(idx, cols, "vendor_id", device::setVendor_id);
                setIfPresentInt(idx, cols, "region_id", device::setRegion_id);
                setIfPresentInt(idx, cols, "city_id", device::setCity_id);
                setIfPresentInt(idx, cols, "district_id", device::setDistrict_id);
                setIfPresentInt(idx, cols, "ward_id", device::setWard_id);
                setIfPresentInt(idx, cols, "store_id", device::setStore_id);

                setIfPresent(idx, cols, "lmx_url", device::setLmx_url);
                setIfPresent(idx, cols, "modcart_url", device::setModcart_url);
                setIfPresentBool(idx, cols, "cachecreatives", device::setCacheCreatives);

                // lastRequestTime (ISO-8601 hoặc pattern yyyy-MM-dd HH:mm:ss)
                if (containsKey(idx, "lastrequesttime")) {
                    String v = getString(idx, cols, "lastrequesttime");
                    if (v != null && !v.isEmpty()) {
                        LocalDateTime dt = tryParseDateTime(v);
                        if (dt != null) {
                            device.setLastRequestTime(dt);
                        }
                    }
                }

                // Các trường dạng JSON
                if (containsKey(idx, "display_area")) {
                    String json = getString(idx, cols, "display_area");
                    if (json != null && !json.isEmpty()) {
                        try {
                            List<DisplayArea> displayAreas = mapper.readValue(json,
                                    new TypeReference<List<DisplayArea>>() {});
                            device.setDisplay_area(displayAreas);
                        } catch (Exception ignored) { /* bỏ qua nếu lỗi parse JSON */ }
                    }
                }

                if (containsKey(idx, "ads_hour_block")) {
                    String json = getString(idx, cols, "ads_hour_block");
                    if (json != null && !json.isEmpty()) {
                        try {
                            List<AdsHourBlock> blocks = mapper.readValue(json,
                                    new TypeReference<List<AdsHourBlock>>() {});
                            device.setAds_hour_block(blocks);
                        } catch (Exception ignored) { /* bỏ qua nếu lỗi parse JSON */ }
                    }
                }

                // multiplier: ưu tiên JSON; nếu không có thì fallback từ các cột đơn
                List<HourlyMultiplier> multipliers = null;
                if (containsKey(idx, "multiplier")) {
                    String json = getString(idx, cols, "multiplier");
                    if (json != null && !json.isEmpty()) {
                        try {
                            multipliers = mapper.readValue(json,
                                    new TypeReference<List<HourlyMultiplier>>() {});
                        } catch (Exception ignored) { /* fallback tiếp */ }
                    }
                }

                if (multipliers == null) {
                    // Fallback 1: multiplier_value (áp cho 7 ngày x 24h)
                    String multiplier = getString(idx, cols, "multiplier");

                    multipliers = new ArrayList<>(24);
                    for (int hour = 0; hour < 24; hour++) {
                        HourlyMultiplier hm = new HourlyMultiplier();
                        hm.setMon(defaultIfEmpty(multiplier, "1"));
                        hm.setTue(defaultIfEmpty(multiplier, "1"));
                        hm.setWed(defaultIfEmpty(multiplier, "1"));
                        hm.setThu(defaultIfEmpty(multiplier, "1"));
                        hm.setFri(defaultIfEmpty(multiplier, "1"));
                        hm.setSat(defaultIfEmpty(multiplier, "1"));
                        hm.setSun(defaultIfEmpty(multiplier, "1"));
                        multipliers.add(hm);
                    }
                }

                if (multipliers != null) {
                    device.setMultiplier(multipliers);
                }

                devices.add(device);
            }
        }

        return devices;
    }

    public static void main(String[] args) {
        String csvPath = "coke-ooh.csv";

        try {
            List<SspDevice> devices = processCsvFile(csvPath);

            System.out.println("Processed " + devices.size() + " devices from: " + csvPath);
            // In nhanh một vài trường để kiểm tra
            devices.stream().limit(500).forEach(d -> {
                System.out.println("- device_id=" + d.getDevice_id() + d);
            });

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // ----------------- Helpers -----------------

    private static boolean containsKey(Map<String, Integer> idx, String key) {
        return idx.containsKey(key.toLowerCase(Locale.ROOT));
    }

    private static String getString(Map<String, Integer> idx, List<String> cols, String key) {
        Integer i = idx.get(key.toLowerCase(Locale.ROOT));
        if (i == null || i < 0 || i >= cols.size()) return null;
        String v = cols.get(i);
        return v != null ? v.trim() : null;
    }

    private static void setIfPresent(Map<String, Integer> idx, List<String> cols, String key,
                                     java.util.function.Consumer<String> setter) {
        String v = getString(idx, cols, key);
        if (v != null && !v.isEmpty()) {
            setter.accept(v);
        }
    }

    private static void setIfPresentInt(Map<String, Integer> idx, List<String> cols, String key,
                                        java.util.function.IntConsumer setter) {
        String v = getString(idx, cols, key);
        if (v != null && !v.isEmpty()) {
            try {
                setter.accept(Integer.parseInt(v));
            } catch (NumberFormatException ignored) {}
        }
    }

    private static void setIfPresentLong(Map<String, Integer> idx, List<String> cols, String key,
                                         java.util.function.LongConsumer setter) {
        String v = getString(idx, cols, key);
        if (v != null && !v.isEmpty()) {
            try {
                setter.accept(Long.parseLong(v));
            } catch (NumberFormatException ignored) {}
        }
    }

    private static void setIfPresentDouble(Map<String, Integer> idx, List<String> cols, String key,
                                           java.util.function.DoubleConsumer setter) {
        String v = getString(idx, cols, key);
        if (v != null && !v.isEmpty()) {
            try {
                setter.accept(Double.parseDouble(v));
            } catch (NumberFormatException ignored) {}
        }
    }

    private static void setIfPresentBool(Map<String, Integer> idx, List<String> cols, String key,
                                         java.util.function.Consumer<Boolean> setter) {
        String v = getString(idx, cols, key);
        if (v != null && !v.isEmpty()) {
            setter.accept(parseBoolean(v));
        }
    }

    private static Boolean parseBoolean(String v) {
        String s = v.trim().toLowerCase(Locale.ROOT);
        return s.equals("1") || s.equals("true") || s.equals("yes") || s.equals("y");
    }

    private static LocalDateTime tryParseDateTime(String s) {
        try {
            return LocalDateTime.parse(s);
        } catch (Exception ignored) {}
        try {
            DateTimeFormatter f = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return LocalDateTime.parse(s, f);
        } catch (Exception ignored) {}
        return null;
    }

    private static boolean anyNonEmpty(String... arr) {
        for (String s : arr) {
            if (s != null && !s.isEmpty()) return true;
        }
        return false;
    }

    private static String defaultIfEmpty(String v, String def) {
        return (v == null || v.isEmpty()) ? def : v;
    }

    // CSV parser đơn giản hỗ trợ dấu phẩy trong ngoặc kép và escape "" -> "
    private static List<String> parseCsvLine(String line) {
        List<String> cols = new ArrayList<>();
        if (line == null) return cols;

        StringBuilder sb = new StringBuilder();
        boolean inQuotes = false;
        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);
            if (inQuotes) {
                if (c == '"') {
                    // kiểm tra escape ""
                    if (i + 1 < line.length() && line.charAt(i + 1) == '"') {
                        sb.append('"');
                        i++;
                    } else {
                        inQuotes = false;
                    }
                } else {
                    sb.append(c);
                }
            } else {
                if (c == '"') {
                    inQuotes = true;
                } else if (c == ',') {
                    cols.add(sb.toString());
                    sb.setLength(0);
                } else {
                    sb.append(c);
                }
            }
        }
        cols.add(sb.toString());
        return cols;
    }
}