package info.nguyenct.adtrue.util;

import info.nguyenct.adtrue.restclient.HttpGetClient;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.security.NoSuchAlgorithmException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

public class HttpGetClientPerformanceTest {
    private static final int THREAD_COUNT = 500;
    private static final int REQUESTS_PER_THREAD = 50;
    private static final String TEST_URL = "http://ssp.adtrue.com.vn/videos/2025-06-30/vad_1465384661.mp4?";
    
    public static void main(String[] args) throws InterruptedException, NoSuchAlgorithmException {
        // Create HttpGetClient with metrics
        HttpGetClient<String> client = new HttpGetClient("performance-test", new SimpleMeterRegistry(), Function.identity());
        
        // Track total requests and errors
        AtomicInteger totalRequests = new AtomicInteger(0);
        AtomicInteger failedRequests = new AtomicInteger(0);
        
        // Use CountDownLatch to wait for all threads to complete
        CountDownLatch latch = new CountDownLatch(THREAD_COUNT);
        
        System.out.println("Starting performance test with " + THREAD_COUNT + 
                " virtual threads, each making " + REQUESTS_PER_THREAD + " requests");
        
        long startTime = System.currentTimeMillis();
        
        // Create and start virtual threads
        for (int i = 0; i < THREAD_COUNT; i++) {
            final int threadId = i;
            Thread.startVirtualThread(() -> {
                try {
                    for (int j = 0; j < REQUESTS_PER_THREAD; j++) {
                        String deviceId = "PERF-TEST-" + threadId + "-" + j;
                        String url = TEST_URL + deviceId;
                        
                        try {
                            var result = client.sendGet(url);
                            totalRequests.incrementAndGet();
                            
                            if (!result.isSuccess()) {
                                failedRequests.incrementAndGet();
                            }
                        } catch (Exception e) {
                            failedRequests.incrementAndGet();
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        // Wait for all threads to complete
        latch.await();
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        // Print results
        System.out.println("\n--- Performance Test Results ---");
        System.out.println("Total execution time: " + totalTime + " ms");
        System.out.println("Total requests: " + totalRequests.get());
        System.out.println("Failed requests: " + failedRequests.get());
        System.out.println("Requests per second: " + 
                (totalRequests.get() * 1000.0 / totalTime));
        
        // Print metrics from HttpGetClient
        System.out.println("\n--- Detailed Metrics ---");
        var metrics = client.getMetrics();
        System.out.println("Requests: " + metrics.requests());
        System.out.println("Success Responses: " + metrics.successResponses());
        System.out.println("Error Responses: " + metrics.errorResponses());
        System.out.println("Success Duration P95 (ms): " + metrics.successDurationP95());
        System.out.println("Failure Duration P95 (ms): " + metrics.failureDurationP95());
    }
}