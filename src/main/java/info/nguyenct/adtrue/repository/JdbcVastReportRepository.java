package info.nguyenct.adtrue.repository;

import info.nguyenct.adtrue.dto.BannerReportResponse;
import info.nguyenct.adtrue.dto.CampaignReportResponse;
import info.nguyenct.adtrue.dto.DetailReportResponse;
import info.nguyenct.adtrue.dto.DeviceReportResponse;
import info.nguyenct.adtrue.model.VastReport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public class JdbcVastReportRepository {
    private static final Logger logger = LoggerFactory.getLogger(JdbcVastReportRepository.class);
    private static final Logger sqlLogger = LoggerFactory.getLogger("sql.debug.JdbcCMReportRepository");

    private final JdbcTemplate jdbcTemplate;

    public JdbcVastReportRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * Batch save (insert or update) nhiều báo cáo cùng lúc
     *
     * @param reports Danh sách báo cáo cần lưu
     */
    public void batchSave(List<VastReport> reports) {
        if (reports.isEmpty()) {
            return;
        }

        long startTime = System.currentTimeMillis();
        logger.debug("Starting batch save of {} reports", reports.size());

        try {
            // Sử dụng CREATE TABLE IF NOT EXISTS thay vì CREATE TABLE
            jdbcTemplate.execute(
                    "CREATE TABLE IF NOT EXISTS temp_vast_report_input (" +
                            "device_id VARCHAR(255), " +
                            "ssp_name VARCHAR(50), " +
                            "campaign_id VARCHAR(255), " +
                            "banner_id VARCHAR(255), " +
                            "event_date TIMESTAMP, " +
                            "channel_id INT, " +
                            "vendor_id INT, " +
                            "region_id INT, " +
                            "city_id INT, " +
                            "district_id INT, " +
                            "ward_id INT, " +
                            "store_id INT, " +
                            "multiplier INT, " +
                            "total_vast_request INT, " +
                            "total_impression INT, " +
                            "total_creative_view INT, " +
                            "total_start INT, " +
                            "total_first_quartile INT, " +
                            "total_midpoint INT, " +
                            "total_third_quartile INT, " +
                            "total_complete INT, " +
                            "total_click_through INT, " +
                            "total_error INT, " +
                            "total_duration INT, " +
                            "time TIMESTAMP)"
            );

            // Xóa dữ liệu cũ trước khi chèn dữ liệu mới
            jdbcTemplate.execute("TRUNCATE TABLE temp_vast_report_input");

            // 2. Insert all reports into temporary table
            jdbcTemplate.batchUpdate(
                    "INSERT INTO temp_vast_report_input VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    new BatchPreparedStatementSetter() {
                        @Override
                        public void setValues(PreparedStatement ps, int i) throws SQLException {
                            VastReport report = reports.get(i);
                            ps.setString(1, report.getDeviceId());
                            ps.setString(2, report.getSspName());
                            ps.setString(3, report.getCampaignId());
                            ps.setString(4, report.getBannerId());
                            ps.setTimestamp(5, Timestamp.valueOf(report.getEventDate()));
                            ps.setInt(6, report.getChannelId());
                            ps.setInt(7, report.getVendorId());
                            ps.setInt(8, report.getRegionId());
                            ps.setInt(9, report.getCityId());
                            ps.setInt(10, report.getDistrictId());
                            ps.setInt(11, report.getWardId());
                            ps.setInt(12, report.getStoreId());
                            ps.setInt(13, report.getMultiplier());
                            ps.setInt(14, report.getTotalVastRequest());
                            ps.setInt(15, report.getTotalImpression());
                            ps.setInt(16, report.getTotalCreativeView());
                            ps.setInt(17, report.getTotalStart());
                            ps.setInt(18, report.getTotalFirstQuartile());
                            ps.setInt(19, report.getTotalMidpoint());
                            ps.setInt(20, report.getTotalThirdQuartile());
                            ps.setInt(21, report.getTotalComplete());
                            ps.setInt(22, report.getTotalClickThrough());
                            ps.setInt(23, report.getTotalError());
                            ps.setInt(24, report.getTotalDuration());
                            ps.setTimestamp(25, Timestamp.valueOf(report.getTime()));
                        }

                        @Override
                        public int getBatchSize() {
                            return reports.size();
                        }
                    }
            );

            // 3. Perform updates for existing reports
            int updatedCount = jdbcTemplate.update(
                    "UPDATE vast_report r " +
                            "SET total_vast_request = r.total_vast_request + t.total_vast_request, " +
                            "total_impression = r.total_impression + t.total_impression, " +
                            "total_creative_view = r.total_creative_view + t.total_creative_view, " +
                            "total_start = r.total_start + t.total_start, " +
                            "total_first_quartile = r.total_first_quartile + t.total_first_quartile, " +
                            "total_midpoint = r.total_midpoint + t.total_midpoint, " +
                            "total_third_quartile = r.total_third_quartile + t.total_third_quartile, " +
                            "total_complete = r.total_complete + t.total_complete, " +
                            "total_click_through = r.total_click_through + t.total_click_through, " +
                            "total_error = r.total_error + t.total_error, " +
                            "total_duration = r.total_duration + t.total_duration, " +
                            "time = t.time " +
                            "FROM temp_vast_report_input t " +
                            "WHERE r.device_id = t.device_id AND " +
                            "r.ssp_name = t.ssp_name AND " +
                            "r.campaign_id = t.campaign_id AND " +
                            "r.banner_id = t.banner_id AND " +
                            "r.event_date = t.event_date AND " +
                            "r.channel_id = t.channel_id AND " +
                            "r.vendor_id = t.vendor_id AND " +
                            "r.region_id = t.region_id AND " +
                            "r.city_id = t.city_id AND " +
                            "r.district_id = t.district_id AND " +
                            "r.ward_id = t.ward_id AND " +
                            "r.store_id = t.store_id AND " +
                            "r.multiplier = t.multiplier"
            );

            // 4. Insert new reports
            int insertedCount = jdbcTemplate.update(
                    "INSERT INTO vast_report (device_id, ssp_name, campaign_id, banner_id, event_date, " +
                            "channel_id, vendor_id, region_id, city_id, district_id, ward_id, store_id, multiplier, " +
                            "total_vast_request, total_impression, total_creative_view, total_start, total_first_quartile, total_midpoint, " +
                            "total_third_quartile, total_complete, total_click_through, total_error, total_duration, time) " +
                            "SELECT t.device_id, t.ssp_name, t.campaign_id, t.banner_id, t.event_date, " +
                            "t.channel_id, t.vendor_id, t.region_id, t.city_id, t.district_id, t.ward_id, t.store_id, t.multiplier, " +
                            "t.total_vast_request, t.total_impression, t.total_creative_view, t.total_start, t.total_first_quartile, t.total_midpoint, " +
                            "t.total_third_quartile, t.total_complete, t.total_click_through, t.total_error, t.total_duration, t.time " +
                            "FROM temp_vast_report_input t " +
                            "LEFT JOIN vast_report r ON " +
                            "t.device_id = r.device_id AND " +
                            "t.ssp_name = r.ssp_name AND " +
                            "t.campaign_id = r.campaign_id AND " +
                            "t.banner_id = r.banner_id AND " +
                            "t.event_date = r.event_date AND " +
                            "t.channel_id = r.channel_id AND " +
                            "t.vendor_id = r.vendor_id AND " +
                            "t.region_id = r.region_id AND " +
                            "t.city_id = r.city_id AND " +
                            "t.district_id = r.district_id AND " +
                            "t.ward_id = r.ward_id AND " +
                            "t.store_id = r.store_id AND " +
                            "t.multiplier = r.multiplier " +
                            "WHERE r.id IS NULL"
            );

            // 5. Drop temporary table
//            jdbcTemplate.execute("DROP TABLE IF EXISTS temp_vast_report_input");

            long duration = System.currentTimeMillis() - startTime;
            logger.info("Batch saved {} reports ({} inserted, {} updated) in {} ms",
                    reports.size(), insertedCount, updatedCount, duration);

        } catch (Exception e) {
            logger.error("Error in batch save operation: {}", e.getMessage(), e);
        }
    }

    /**
     * Report theo list banner
     */
    public List<BannerReportResponse> getBannerReport(LocalDate fdate, LocalDate tdate,
                                                      List<String> bannerIds, Map<String, Object> filters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT banner_id, SUM(total_complete) as actual_spot, SUM(total_complete*multiplier) as impression, 0 as reach, ");
        sql.append("COUNT(DISTINCT device_id) as device_count, ");
        sql.append("COUNT(DISTINCT channel_id) as channel_count ");
        sql.append("FROM vast_report ");
        sql.append("WHERE total_complete > 0 AND (event_date BETWEEN ? AND ? ) ");

        List<Object> params = new ArrayList<>();
        params.add(Timestamp.valueOf(fdate.atStartOfDay()));
        params.add(Timestamp.valueOf(tdate.plusDays(1).atStartOfDay()));

        if (bannerIds != null && !bannerIds.isEmpty()) {
            sql.append("AND banner_id IN (");
            for (int i = 0; i < bannerIds.size(); i++) {
                if (i > 0) sql.append(",");
                sql.append("?");
                params.add(bannerIds.get(i));
            }
            sql.append(") ");
        }

        // Add dynamic filters
        addFiltersToQuery(sql, params, filters);

        sql.append("GROUP BY banner_id ");
        sql.append("ORDER BY banner_id");

        String finalSql = sql.toString();
        logSql(finalSql, params.toArray());
        return jdbcTemplate.query(finalSql, new BannerReportRowMapper(), params.toArray());
    }

    /**
     * Report theo list campaign
     */
    public List<CampaignReportResponse> getCampaignReport(LocalDate fdate, LocalDate tdate,
                                                          List<String> campaignIds, Map<String, Object> filters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT campaign_id, SUM(total_complete) as actual_spot, SUM(total_complete*multiplier) as impression, 0 as reach, ");
        sql.append("COUNT(DISTINCT device_id) as device_count, ");
        sql.append("COUNT(DISTINCT channel_id) as channel_count ");
        sql.append("FROM vast_report ");
        sql.append("WHERE total_complete > 0 AND (event_date BETWEEN ? AND ? ) ");

        List<Object> params = new ArrayList<>();
        params.add(Timestamp.valueOf(fdate.atStartOfDay()));
        params.add(Timestamp.valueOf(tdate.plusDays(1).atStartOfDay()));

        if (campaignIds != null && !campaignIds.isEmpty()) {
            sql.append("AND campaign_id IN (");
            for (int i = 0; i < campaignIds.size(); i++) {
                if (i > 0) sql.append(",");
                sql.append("?");
                params.add(campaignIds.get(i));
            }
            sql.append(") ");
        }

        // Add dynamic filters
        addFiltersToQuery(sql, params, filters);

        sql.append("GROUP BY campaign_id ");
        sql.append("ORDER BY campaign_id");

        String finalSql = sql.toString();
        logSql(finalSql, params.toArray());

        return jdbcTemplate.query(sql.toString(), new CampaignReportRowMapper(), params.toArray());
    }

    /**
     * Get detailed report for a specific campaign with grouping options
     */
    public List<DetailReportResponse> getCampaignDetailReport(LocalDate fdate, LocalDate tdate,
                                                              String campaignId, String groupBy,
                                                              Map<String, Object> filters) {
        StringBuilder sql = new StringBuilder();

        // Handle day grouping specially
        String selectField;
        if ("day".equals(groupBy)) {
            selectField = "TO_CHAR(event_date, 'YYYY-MM-DD') as group_key";
        } else {
            selectField = groupBy + " as group_key";
        }

        sql.append("SELECT ").append(selectField).append(", SUM(total_complete) as actual_spot, SUM(total_complete*multiplier) as impression, 0 as reach, ");
        sql.append("COUNT(DISTINCT device_id) as device_count, ");
        sql.append("COUNT(DISTINCT channel_id) as channel_count ");
        sql.append("FROM vast_report ");
        sql.append("WHERE total_complete > 0 AND (event_date BETWEEN ? AND ? ) ");
        sql.append("AND campaign_id = ? ");

        List<Object> params = new ArrayList<>();
        params.add(Timestamp.valueOf(fdate.atStartOfDay()));
        params.add(Timestamp.valueOf(tdate.plusDays(1).atStartOfDay()));
        params.add(campaignId);

        // Add dynamic filters
        addFiltersToQuery(sql, params, filters);

        // Add group by clause
        if ("day".equals(groupBy)) {
            sql.append("GROUP BY TO_CHAR(event_date, 'YYYY-MM-DD') ");
            sql.append("ORDER BY TO_CHAR(event_date, 'YYYY-MM-DD')");
        } else {
            sql.append("GROUP BY ").append(groupBy).append(" ");
            sql.append("ORDER BY ").append(groupBy);
        }

        String finalSql = sql.toString();
        logSql(finalSql, params.toArray());

        return jdbcTemplate.query(finalSql, new CampaignDetailReportRowMapper(), params.toArray());
    }

    /**
     * Get detailed report for a specific banner with grouping options
     */
    public List<DetailReportResponse> getBannerDetailReport(LocalDate fdate, LocalDate tdate,
                                                            String bannerId, String groupBy,
                                                            Map<String, Object> filters) {
        StringBuilder sql = new StringBuilder();

        // Handle day grouping specially
        String selectField;
        if ("day".equals(groupBy)) {
            selectField = "TO_CHAR(event_date, 'YYYY-MM-DD') as group_key";
        } else {
            selectField = groupBy + " as group_key";
        }

        sql.append("SELECT ").append(selectField).append(", SUM(total_complete) as actual_spot, SUM(total_complete*multiplier) as impression, 0 as reach, ");
        sql.append("COUNT(DISTINCT device_id) as device_count, ");
        sql.append("COUNT(DISTINCT channel_id) as channel_count ");
        sql.append("FROM vast_report ");
        sql.append("WHERE total_complete > 0 AND (event_date BETWEEN ? AND ? ) ");
        sql.append("AND banner_id = ? ");

        List<Object> params = new ArrayList<>();
        params.add(Timestamp.valueOf(fdate.atStartOfDay()));
        params.add(Timestamp.valueOf(tdate.plusDays(1).atStartOfDay()));
        params.add(bannerId);

        // Add dynamic filters
        addFiltersToQuery(sql, params, filters);

        // Add group by clause
        if ("day".equals(groupBy)) {
            sql.append("GROUP BY TO_CHAR(event_date, 'YYYY-MM-DD') ");
            sql.append("ORDER BY TO_CHAR(event_date, 'YYYY-MM-DD')");
        } else {
            sql.append("GROUP BY ").append(groupBy).append(" ");
            sql.append("ORDER BY ").append(groupBy);
        }

        String finalSql = sql.toString();
        logSql(finalSql, params.toArray());

        return jdbcTemplate.query(finalSql, new CampaignDetailReportRowMapper(), params.toArray());
    }

    /**
     * Get detailed report for devices with grouping options
     */
    public List<DeviceReportResponse> getDeviceDetailReport(LocalDate fdate, LocalDate tdate,
                                                            String campaignId, String bannerId, String groupBy,
                                                            Map<String, Object> filters) {
        StringBuilder sql = new StringBuilder();

        // Handle day and hour grouping specially
        String selectField;
        String groupByField;
        String orderByField;
        if ("day".equals(groupBy)) {
            selectField = "TO_CHAR(event_date, 'YYYY-MM-DD') as day_hour, device_id";
            groupByField = "TO_CHAR(event_date, 'YYYY-MM-DD'), device_id";
            orderByField = "TO_CHAR(event_date, 'YYYY-MM-DD') desc, device_id asc";
        } else {
            selectField = "TO_CHAR(event_date, 'YYYY-MM-DD HH24')  as day_hour, device_id";
            groupByField = "TO_CHAR(event_date, 'YYYY-MM-DD HH24'), device_id";
            orderByField = "TO_CHAR(event_date, 'YYYY-MM-DD HH24') desc, device_id asc";
        }

        //TODO: bổ sung thêm các trường khác
        sql.append("SELECT ").append(selectField).append(", " +
                "SUM(total_vast_request) as vast_request, " +
                "SUM(total_impression) as impression_events, " +
                "SUM(total_creative_view) as creative_view, " +
                "sum(total_start) as start, " +
                "sum(total_first_quartile) as first_quartile, " +
                "sum(total_midpoint) as midpoint, " +
                "sum(total_third_quartile) as third_quartile, " +
                "SUM(total_complete) as complete, " +
                "SUM(total_click_through) as click_through, " +
                "SUM(total_error) as error, " +
                "SUM(total_duration) as duration, " +
                "SUM(total_complete*multiplier) as impression, 0 as reach, ");
        sql.append("COUNT(DISTINCT device_id) as device_count, ");
        sql.append("COUNT(DISTINCT channel_id) as channel_count ");
        sql.append("FROM vast_report ");
        sql.append("WHERE (event_date BETWEEN ? AND ? ) ");

        List<Object> params = new ArrayList<>();
        params.add(Timestamp.valueOf(fdate.atStartOfDay()));
        params.add(Timestamp.valueOf(tdate.plusDays(1).atStartOfDay()));

        // Add campaign_id filter if provided
        if (campaignId != null && !campaignId.isEmpty()) {
            sql.append("AND campaign_id = ? ");
            params.add(campaignId);
        }

        // Add banner_id filter if provided
        if (bannerId != null && !bannerId.isEmpty()) {
            sql.append("AND banner_id = ? ");
            params.add(bannerId);
        }

        // Add dynamic filters
        addFiltersToQuery(sql, params, filters);

        // Add group by clause
        sql.append("GROUP BY ").append(groupByField).append(" ");
        sql.append("ORDER BY ").append(orderByField);

        String finalSql = sql.toString();
        logSql(finalSql, params.toArray());

        return jdbcTemplate.query(finalSql, new DeviceReportResponseRowMapper(), params.toArray());
    }

    /**
     * Helper method to add dynamic filters to query
     */
    private void addFiltersToQuery(StringBuilder sql, List<Object> params, Map<String, Object> filters) {
        if (filters != null) {
            if (filters.containsKey("channel_id")) {
                sql.append("AND channel_id = ? ");
                params.add(filters.get("channel_id"));
            }
            if (filters.containsKey("vendor_id")) {
                sql.append("AND vendor_id = ? ");
                params.add(filters.get("vendor_id"));
            }
            if (filters.containsKey("region_id")) {
                sql.append("AND region_id = ? ");
                params.add(filters.get("region_id"));
            }
            if (filters.containsKey("city_id")) {
                sql.append("AND city_id = ? ");
                params.add(filters.get("city_id"));
            }
            if (filters.containsKey("district_id")) {
                sql.append("AND district_id = ? ");
                params.add(filters.get("district_id"));
            }
            if (filters.containsKey("ward_id")) {
                sql.append("AND ward_id = ? ");
                params.add(filters.get("ward_id"));
            }
            if (filters.containsKey("store_id")) {
                sql.append("AND store_id = ? ");
                params.add(filters.get("store_id"));
            }
        }
    }

    private static class BannerReportRowMapper implements RowMapper<BannerReportResponse> {
        @Override
        public BannerReportResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
            BannerReportResponse response = new BannerReportResponse();
            response.setBannerId(rs.getString("banner_id"));
            response.setActualSpot(rs.getLong("actual_spot"));
            response.setImpression(rs.getLong("impression"));
            response.setReach(rs.getLong("reach"));
            response.setDeviceCount(rs.getInt("device_count"));
            response.setChannelCount(rs.getInt("channel_count"));
            return response;
        }
    }

    private static class CampaignReportRowMapper implements RowMapper<CampaignReportResponse> {
        @Override
        public CampaignReportResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
            CampaignReportResponse response = new CampaignReportResponse();
            response.setCampaignId(rs.getString("campaign_id"));
            response.setActualSpot(rs.getLong("actual_spot"));
            response.setImpression(rs.getLong("impression"));
            response.setReach(rs.getLong("reach"));
            response.setDeviceCount(rs.getInt("device_count"));
            response.setChannelCount(rs.getInt("channel_count"));
            return response;
        }
    }

    private static class CampaignDetailReportRowMapper implements RowMapper<DetailReportResponse> {
        @Override
        public DetailReportResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
            DetailReportResponse response = new DetailReportResponse();
            response.setGroupKey(rs.getString("group_key"));
            response.setActualSpot(rs.getLong("actual_spot"));
            response.setImpression(rs.getLong("impression"));
            response.setReach(rs.getLong("reach"));
            response.setDeviceCount(rs.getInt("device_count"));
            response.setChannelCount(rs.getInt("channel_count"));
            return response;
        }
    }

    private static class DeviceReportResponseRowMapper implements RowMapper<DeviceReportResponse> {
        @Override
        public DeviceReportResponse mapRow(ResultSet rs, int rowNum) throws SQLException {
            DeviceReportResponse response = new DeviceReportResponse();
            response.setDeviceId(rs.getString("device_id"));
            response.setDayHour(rs.getString("day_hour"));
            response.setVastRequest(rs.getLong("vast_request"));
            response.setVastImpression(rs.getLong("impression_events"));
            response.setCreativeView(rs.getLong("creative_view"));
            response.setStart(rs.getLong("start"));
            response.setFirstQuartile(rs.getLong("first_quartile"));
            response.setMidpoint(rs.getLong("midpoint"));
            response.setThirdQuartile(rs.getLong("third_quartile"));
            response.setComplete(rs.getLong("complete"));
            response.setClickThrough(rs.getLong("click_through"));
            response.setError(rs.getLong("error"));
            response.setImpression(rs.getLong("impression"));
            response.setReach(rs.getLong("reach"));
            response.setDeviceCount(rs.getInt("device_count"));
            response.setChannelCount(rs.getInt("channel_count"));
            return response;
        }
    }

    /**
     * Log SQL query for debugging
     * @param sql SQL query to log
     * @param params Query parameters (optional)
     */
    private void logSql(String sql, Object... params) {
        if (sqlLogger.isDebugEnabled()) {
            StringBuilder logMessage = new StringBuilder("Executing SQL: ").append(sql);
            if (params != null && params.length > 0) {
                logMessage.append(" with params: [");
                for (int i = 0; i < params.length; i++) {
                    if (i > 0) logMessage.append(", ");
                    logMessage.append(params[i]);
                }
                logMessage.append("]");
            }
            sqlLogger.debug(logMessage.toString());
        }
    }

}