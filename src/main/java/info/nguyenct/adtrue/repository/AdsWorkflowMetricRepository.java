package info.nguyenct.adtrue.repository;

import info.nguyenct.adtrue.model.AdsWorkflowMetricRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.sql.ResultSet;
import java.sql.SQLException;

@Repository
public class AdsWorkflowMetricRepository {
    private static final Logger logger = LoggerFactory.getLogger(AdsWorkflowMetricRepository.class);
    
    private final JdbcTemplate jdbcTemplate;
    
    public AdsWorkflowMetricRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
    
    public void save(AdsWorkflowMetricRecord metric) {
        long startTime = System.currentTimeMillis();
        logger.debug("Saving AdsGettingWorkflow metrics to database");
        
        jdbcTemplate.update(
            "INSERT INTO ads_workflow_metrics (collected_at, ads_requests, cm_ads_responses, " +
            "vistar_ads_responses, lmx_ads_responses, adtrue_ads_responses, modcart_ads_responses, cache_size, " +
            "cm_ads_request_duration_p95, vistar_ads_request_duration_p95, lmx_ads_request_duration_p95, " +
            "modcart_ads_request_duration_p95) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            Timestamp.valueOf(metric.getCollectedAt()),
            metric.getAdsRequests(),
            metric.getCmAdsResponses(),
            metric.getVistarAdsResponses(),
            metric.getLmxAdsResponses(),
            metric.getAdtrueAdsResponses(),
            metric.getModcartAdsResponses(),
            metric.getCacheSize(),
            metric.getCmAdsRequestDurationP95(),
            metric.getVistarAdsRequestDurationP95(),
            metric.getLmxAdsRequestDurationP95(),
            metric.getModcartAdsRequestDurationP95()
        );
        
        long duration = System.currentTimeMillis() - startTime;
        logger.info("Saved AdsGettingWorkflow metrics in {} ms", duration);
    }

    /**
     * Maps a database row to an AdsWorkflowMetricRecord
     */
    private static class AdsWorkflowMetricRowMapper implements RowMapper<AdsWorkflowMetricRecord> {
        @Override
        public AdsWorkflowMetricRecord mapRow(ResultSet rs, int rowNum) throws SQLException {
            AdsWorkflowMetricRecord record = new AdsWorkflowMetricRecord();
            record.setId(rs.getLong("id"));
            record.setCollectedAt(rs.getTimestamp("collected_at").toLocalDateTime());
            record.setAdsRequests(rs.getInt("ads_requests"));
            record.setCmAdsResponses(rs.getInt("cm_ads_responses"));
            record.setVistarAdsResponses(rs.getInt("vistar_ads_responses"));
            record.setLmxAdsResponses(rs.getInt("lmx_ads_responses"));
            record.setAdtrueAdsResponses(rs.getInt("adtrue_ads_responses"));
            record.setModcartAdsResponses(rs.getInt("modcart_ads_responses"));
            record.setCacheSize(rs.getInt("cache_size"));
            record.setCmAdsRequestDurationP95(rs.getInt("cm_ads_request_duration_p95"));
            record.setVistarAdsRequestDurationP95(rs.getInt("vistar_ads_request_duration_p95"));
            record.setLmxAdsRequestDurationP95(rs.getInt("lmx_ads_request_duration_p95"));
            record.setModcartAdsRequestDurationP95(rs.getInt("modcart_ads_request_duration_p95"));
            return record;
        }
    }
}
