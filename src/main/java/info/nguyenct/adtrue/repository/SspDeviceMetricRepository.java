package info.nguyenct.adtrue.repository;

import info.nguyenct.adtrue.model.SspDevice;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public class SspDeviceMetricRepository {
    private static final Logger logger = LoggerFactory.getLogger(SspDeviceMetricRepository.class);
    
    private final JdbcTemplate jdbcTemplate;
    
    public SspDeviceMetricRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public void batchSave(List<SspDevice> devices, LocalDateTime collectedAt) {
        if (devices.isEmpty()) {
            return;
        }

        long startTime = System.currentTimeMillis();
        logger.debug("Starting batch save of {} device metrics", devices.size());

        jdbcTemplate.batchUpdate(
                "INSERT INTO ssp_device_metrics (device_id, channel_id, vendor_id, region_id, " +
                        "city_id, district_id, ward_id, store_id, multiplier, last_request_time, " +
                        "collected_at, in_ads_block_hour) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                new BatchPreparedStatementSetter() {
                    @Override
                    public void setValues(PreparedStatement ps, int i) throws SQLException {
                        SspDevice device = devices.get(i);
                        ps.setString(1, device.getDevice_id());
                        ps.setInt(2, device.getChannel_id());
                        ps.setInt(3, device.getVendor_id());
                        ps.setInt(4, device.getRegion_id());
                        ps.setInt(5, device.getCity_id());
                        ps.setInt(6, device.getDistrict_id());
                        ps.setInt(7, device.getWard_id());
                        ps.setInt(8, device.getStore_id());
                        ps.setInt(9, device.getMultiplier());
                        ps.setTimestamp(10, Timestamp.valueOf(device.getLastRequestTime()));
                        ps.setTimestamp(11, Timestamp.valueOf(collectedAt));
                        ps.setInt(12, device.isInAdsHourBlock() ? 1 : 0);
                    }

                    @Override
                    public int getBatchSize() {
                        return devices.size();
                    }
                }
        );

        long duration = System.currentTimeMillis() - startTime;
        logger.info("Batch saved {} device metrics in {} ms", devices.size(), duration);
    }
}