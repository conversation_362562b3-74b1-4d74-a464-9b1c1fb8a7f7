package info.nguyenct.adtrue.repository;

import info.nguyenct.adtrue.ssp.vistar.VistarMetricRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;

@Repository
public class VistarMetricRepository {
    private static final Logger logger = LoggerFactory.getLogger(VistarMetricRepository.class);
    
    private final JdbcTemplate jdbcTemplate;
    
    public VistarMetricRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
    
    /**
     * Lưu một bản ghi metric vào database
     * @param metric Bản ghi metric cần lưu
     */
    public void save(VistarMetricRecord metric) {
        long startTime = System.currentTimeMillis();
        logger.debug("Saving Vistar metrics to database");
        
        jdbcTemplate.update(
            "INSERT INTO vistar_metrics (collected_at, ads_requests_sent, ads_response_received, " +
            "ads_response_status_ok, ads_response_status_non_ok, ads_slot_requests, ads_slot_responses, " +
            "proof_of_play_calls, proof_of_play_status_ok, proof_of_play_status_non_ok, " +
            "pending_proof_of_play_calls, device_count, ads_request_duration_p95, proof_of_play_duration_p95, " +
            "creative_cache_duration_p95) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            Timestamp.valueOf(metric.getCollectedAt()),
            metric.getAdsRequestsSent(),
            metric.getAdsResponseReceived(),
            metric.getAdsResponseStatusOk(),
            metric.getAdsResponseStatusNonOk(),
            metric.getAdsSlotRequests(),
            metric.getAdsSlotResponses(),
            metric.getProofOfPlayCalls(),
            metric.getProofOfPlayStatusOk(),
            metric.getProofOfPlayStatusNonOk(),
            metric.getPendingProofOfPlayCalls(),
            metric.getDeviceCount(),
            metric.getAdsRequestDurationP95(),
            metric.getProofOfPlayDurationP95(),
            metric.getCreativeCacheDurationP95()
        );
        
        long duration = System.currentTimeMillis() - startTime;
        logger.info("Saved Vistar metrics in {} ms", duration);
    }

    /**
     * RowMapper để chuyển đổi ResultSet thành VistarMetricRecord
     */
    private static class VistarMetricRowMapper implements RowMapper<VistarMetricRecord> {
        @Override
        public VistarMetricRecord mapRow(ResultSet rs, int rowNum) throws SQLException {
            VistarMetricRecord record = new VistarMetricRecord();
            record.setId(rs.getLong("id"));
            record.setCollectedAt(rs.getTimestamp("collected_at").toLocalDateTime());
            record.setAdsRequestsSent(rs.getInt("ads_requests_sent"));
            record.setAdsResponseReceived(rs.getInt("ads_response_received"));
            record.setAdsResponseStatusOk(rs.getInt("ads_response_status_ok"));
            record.setAdsResponseStatusNonOk(rs.getInt("ads_response_status_non_ok"));
            record.setAdsSlotRequests(rs.getInt("ads_slot_requests"));
            record.setAdsSlotResponses(rs.getInt("ads_slot_responses"));
            record.setProofOfPlayCalls(rs.getInt("proof_of_play_calls"));
            record.setProofOfPlayStatusOk(rs.getInt("proof_of_play_status_ok"));
            record.setProofOfPlayStatusNonOk(rs.getInt("proof_of_play_status_non_ok"));
            record.setPendingProofOfPlayCalls(rs.getInt("pending_proof_of_play_calls"));
            record.setDeviceCount(rs.getInt("device_count"));
            return record;
        }
    }
}