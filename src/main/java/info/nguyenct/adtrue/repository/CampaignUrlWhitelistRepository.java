package info.nguyenct.adtrue.repository;

import info.nguyenct.adtrue.model.CampaignUrlWhitelist;
import info.nguyenct.adtrue.util.Util;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

@Repository
public class CampaignUrlWhitelistRepository {
    
    private final JdbcTemplate jdbcTemplate;
    
    public CampaignUrlWhitelistRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
    
    public List<CampaignUrlWhitelist> findByCampaignId(String campaignId) {
        String sql = "SELECT * FROM campaign_url_whitelist WHERE campaign_id = ?";
        return jdbcTemplate.query(sql, new CampaignUrlWhitelistRowMapper(), campaignId);
    }
    
    public boolean existsByCampaignIdAndUrl(String campaignId, String url) {
        String sql = "SELECT COUNT(*) FROM campaign_url_whitelist WHERE campaign_id = ? AND url = ?";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, campaignId, url);
        return count != null && count > 0;
    }
    
    public void insertUrl(String campaignId, String url, String mediaPath) {
        String sql = "INSERT INTO campaign_url_whitelist (campaign_id, url, media_path, created_at) VALUES (?, ?, ?, ?, ?)";
        long timestamp = Util.getTimeStampNow();
        jdbcTemplate.update(sql, campaignId, url, mediaPath, timestamp, timestamp);
    }
    
    public void deleteUrls(String campaignId, List<String> urls) {
        if (urls.isEmpty()) {
            return;
        }
        
        String sql = "DELETE FROM campaign_url_whitelist WHERE campaign_id = ? AND url = ?";
        
        jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                ps.setString(1, campaignId);
                ps.setString(2, urls.get(i));
            }
            
            @Override
            public int getBatchSize() {
                return urls.size();
            }
        });
    }
    
    private static class CampaignUrlWhitelistRowMapper implements RowMapper<CampaignUrlWhitelist> {
        @Override
        public CampaignUrlWhitelist mapRow(ResultSet rs, int rowNum) throws SQLException {
            CampaignUrlWhitelist whitelist = new CampaignUrlWhitelist();
            whitelist.setId(rs.getLong("id"));
            whitelist.setCampaignId(rs.getString("campaign_id"));
            whitelist.setUrl(rs.getString("url"));
            whitelist.setCreatedAt(rs.getLong("created_at"));
            return whitelist;
        }
    }
}