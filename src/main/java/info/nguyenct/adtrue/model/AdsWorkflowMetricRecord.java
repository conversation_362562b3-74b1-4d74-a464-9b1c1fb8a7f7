package info.nguyenct.adtrue.model;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.Map;

@Data
public class AdsWorkflowMetricRecord {
    private Long id;
    private LocalDateTime collectedAt = LocalDateTime.now();
    private int adsRequests;
    private int cmAdsResponses;
    private int vistarAdsResponses;
    private int adtrueAdsResponses;
    private int lmxAdsResponses;
    private int modcartAdsResponses;
    private int cacheSize;
    private int cmAdsRequestDurationP95;
    private int vistarAdsRequestDurationP95;
    private int lmxAdsRequestDurationP95;
    private int modcartAdsRequestDurationP95;

    public AdsWorkflowMetricRecord() {
    }

    public AdsWorkflowMetricRecord(Map<String, Integer> metrics) {
        this.collectedAt = LocalDateTime.now();
        this.adsRequests = metrics.getOrDefault("ads_requests", 0);
        this.cmAdsResponses = metrics.getOrDefault("cm_ads_responses", 0);
        this.vistarAdsResponses = metrics.getOrDefault("vistar_ads_responses", 0);
        this.lmxAdsResponses = metrics.getOrDefault("lmx_ads_responses", 0);
        this.adtrueAdsResponses = metrics.getOrDefault("adtrue_ads_responses", 0);
        this.cacheSize = metrics.getOrDefault("cache_size", 0);
        this.cmAdsRequestDurationP95 = metrics.getOrDefault("cm_ads_request_duration_p95", 0);
        this.vistarAdsRequestDurationP95 = metrics.getOrDefault("vistar_ads_request_duration_p95", 0);
        this.lmxAdsRequestDurationP95 = metrics.getOrDefault("lmx_ads_request_duration_p95", 0);
        this.modcartAdsResponses = metrics.getOrDefault("modcart_ads_responses", 0);
        this.modcartAdsRequestDurationP95 = metrics.getOrDefault("modcart_ads_request_duration_p95", 0);
    }
}
