package info.nguyenct.adtrue.model;

import info.nguyenct.adtrue.ssp.cm.CMSsp.SpotTracker;
import lombok.Data;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Data
public class SspDeviceData {
    private String deviceId;
    
    // Cached ad from AdsGettingWorkflow
    private AdtrueAd cachedAd;
    private long cachedAdExpiry;
    
    // CM SSP spot trackers (bannerId -> SpotTracker)
    private Map<Integer, SpotTracker> cmSpotTrackers = new ConcurrentHashMap<>();
    
    public SspDeviceData(String deviceId) {
        this.deviceId = deviceId;
    }
    
    public void setCachedAd(AdtrueAd ad, int expiryMinutes) {
        this.cachedAd = ad;
        this.cachedAdExpiry = System.currentTimeMillis() + (expiryMinutes * 60 * 1000);
    }
    
    public boolean isCachedAdValid() {
        return cachedAd != null && System.currentTimeMillis() < cachedAdExpiry;
    }
    
    public void addCmSpotTracker(int bannerId, SpotTracker tracker) {
        cmSpotTrackers.put(bannerId, tracker);
    }
    
    public SpotTracker getCmSpotTracker(int bannerId) {
        return cmSpotTrackers.get(bannerId);
    }
}