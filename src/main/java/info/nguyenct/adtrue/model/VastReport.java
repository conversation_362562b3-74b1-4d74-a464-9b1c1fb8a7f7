package info.nguyenct.adtrue.model;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class VastReport {
    private Long id;
    private String deviceId;
    private String sspName;
    private String campaignId;
    private String bannerId;
    private int channelId;
    private int vendorId;
    private int regionId;
    private int cityId;
    private int districtId;
    private int wardId;
    private int storeId;
    private int multiplier;
    private LocalDateTime eventDate;
    private int totalVastRequest;
    private int totalImpression;
    private int totalCreativeView;
    private int totalStart;
    private int totalFirstQuartile;
    private int totalMidpoint;
    private int totalThirdQuartile;
    private int totalComplete;
    private int totalClickThrough;
    private int totalError;
    private int totalPayout;
    private int totalPayoutError;
    private int totalDuration;
    private LocalDateTime time;

    public VastReport() {
        this.totalVastRequest = 0;
        this.totalImpression = 0;
        this.totalCreativeView = 0;
        this.totalStart = 0;
        this.totalFirstQuartile = 0;
        this.totalMidpoint = 0;
        this.totalThirdQuartile = 0;
        this.totalComplete = 0;
        this.totalClickThrough = 0;
        this.totalError = 0;
    }

    public double getCompleteRate() {
        return totalVastRequest > 0 ? (double) totalComplete / totalVastRequest : 0;
    }
}