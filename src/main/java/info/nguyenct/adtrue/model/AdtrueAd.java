package info.nguyenct.adtrue.model;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
public class AdtrueAd {
    private String adId;          // Unique ID with SSP prefix
    private String campaignId;    // Campaign ID
    private String bannerId;      // Banner ID
    private String assetUrl;      // URL to ad asset
    private String mediaPath;      // Optional video URL (relative path)
    private String width;         // Width of ad
    private String height;        // Height of ad
    private String bitrate;       // Bitrate for video
    private String lengthInSeconds; // Duration in seconds
    private String mimeType;      // MIME type of asset
    private String sspPrefix;       // Type of SSP (vistar, adtrue, etc.)

    // VAST-specific fields
    private String adSystem;
    private String adTitle;
    private int duration;

    // Tracking URLs - support multiple URLs per event
    private List<String> impressionUrls = new ArrayList<>();
    private List<String> errorUrls = new ArrayList<>();
    private List<String> startUrls = new ArrayList<>();
    private List<String> firstQuartileUrls = new ArrayList<>();
    private List<String> midpointUrls = new ArrayList<>();
    private List<String> thirdQuartileUrls = new ArrayList<>();
    private List<String> completeUrls = new ArrayList<>();
    private List<String> creativeViewUrls = new ArrayList<>();
    private List<String> clickThroughUrls = new ArrayList<>();

    // Media files information
    private List<MediaFileInfo> mediaFiles = new ArrayList<>();

    @Data
    @Builder
    public static class MediaFileInfo {
        private String url;
        private String path;
        private String delivery;
        private String type;
        private int bitrate;
        private int width;
        private int height;
        private boolean scalable;
        private boolean maintainAspectRatio;
        private String id;
    }

    // Utility methods for tracking URLs
    public void addTrackingUrl(VastEventType eventType, String url) {
        if (url == null || url.isEmpty() || eventType == null) {
            return;
        }
        
        switch (eventType) {
            case IMPRESSION:
                impressionUrls.add(url);
                break;
            case ERROR:
                errorUrls.add(url);
                break;
            case START:
                startUrls.add(url);
                break;
            case FIRST_QUARTILE:
                firstQuartileUrls.add(url);
                break;
            case MIDPOINT:
                midpointUrls.add(url);
                break;
            case THIRD_QUARTILE:
                thirdQuartileUrls.add(url);
                break;
            case COMPLETE:
                completeUrls.add(url);
                break;
            case CREATIVE_VIEW:
                creativeViewUrls.add(url);
                break;
        }
    }

    public List<String> getTrackingUrls(VastEventType eventType) {
        switch (eventType) {
            case IMPRESSION: return impressionUrls;
            case ERROR: return errorUrls;
            case START: return startUrls;
            case FIRST_QUARTILE: return firstQuartileUrls;
            case MIDPOINT: return midpointUrls;
            case THIRD_QUARTILE: return thirdQuartileUrls;
            case COMPLETE: return completeUrls;
            case CREATIVE_VIEW: return creativeViewUrls;
            default: return new ArrayList<>();
        }
    }
}
