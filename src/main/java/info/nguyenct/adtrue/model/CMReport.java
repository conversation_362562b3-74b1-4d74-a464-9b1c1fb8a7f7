package info.nguyenct.adtrue.model;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class CMReport {
    private Long id;
    private String deviceId;
    private String bannerId;
    private String campaignId;
    private int channelId;
    private int vendorId;
    private int regionId;
    private int cityId;
    private int districtId;
    private int wardId;
    private int storeId;
    private int multiplier;
    private LocalDateTime eventDate;
    private int totalSpot;
    private int totalVastRequest;
    private int totalStart;
    private int totalFirstQuartile;
    private int totalMidpoint;
    private int totalThirdQuartile;
    private int totalComplete;
    private int totalDuration;
    private LocalDateTime time;
    
    public CMReport() {
        this.totalSpot = 0;
        this.totalVastRequest = 0;
        this.totalStart = 0;
        this.totalFirstQuartile = 0;
        this.totalMidpoint = 0;
        this.totalThirdQuartile = 0;
        this.totalComplete = 0;
        this.channelId = 0;
        this.vendorId = 0;
        this.regionId = 0;
        this.cityId = 0;
        this.districtId = 0;
        this.wardId = 0;
        this.storeId = 0;
        this.multiplier = 1;
    }
    
    public double getCompleteRate() {
        return totalSpot > 0 ? (double) totalComplete / totalSpot : 0;
    }
}