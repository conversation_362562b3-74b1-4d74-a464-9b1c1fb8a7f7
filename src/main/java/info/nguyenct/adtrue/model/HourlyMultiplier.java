package info.nguyenct.adtrue.model;

import lombok.Data;

@Data
public class HourlyMultiplier {
    private String mon;
    private String tue;
    private String wed;
    private String thu;
    private String fri;
    private String sat;
    private String sun;
    
    public int getMultiplierForDay(String day) {
        switch (day) {
            case "mon": return parseMultiplier(mon);
            case "tue": return parseMultiplier(tue);
            case "wed": return parseMultiplier(wed);
            case "thu": return parseMultiplier(thu);
            case "fri": return parseMultiplier(fri);
            case "sat": return parseMultiplier(sat);
            case "sun": return parseMultiplier(sun);
            default: return 1;
        }
    }
    
    private int parseMultiplier(String value) {
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return 1;
        }
    }
}