package info.nguyenct.adtrue.model;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

import info.nguyenct.adtrue.util.Util;
import lombok.Data;

@Data
public class SspDevice {
    private String device_id;
    private String mac;
    private String venue_id;
    private List<DisplayArea> display_area;
    private Long display_time;
    private double latitude;
    private double longitude;
    private int ads_duration_hourly;
    private String ads_file_type;
    private List<AdsHourBlock> ads_hour_block = new ArrayList<>();

    private int channel_id;
    private int vendor_id;
    private int region_id;
    private int city_id;
    private int district_id;
    private int ward_id;
    private int store_id;
    private List<HourlyMultiplier> multiplier;

    private String lmx_url = "https://pub.lmx.ai/ssp-project/api/v2/rest/dv/a?refId=VNM-ATT-D-00000-35277&deal=GD-00000-06474&lineItemId=6853d8d4e99bb710e1811e6c";
    private String modcart_url = "https://ondemand-apac.zeststack.com/bid?rtb_seat_id=02756&secret_key=2aA76G5&appid=2756&type=vast&env=app&pubid=2756&width=1920&height=1080&domain=winmart.vn&r=https%3A%2F%2Fwww.winmart.vn&store=Office__ATT-OFC-FL2-TEZ-59Q0D9_ATT-OFC-FL2-TEZ-59Q0D9&developer=winmart.vn&bundle=ATT-OFC-FL2-TEZ-59Q0D9";
    private boolean cacheCreatives = false;

    private LocalDateTime lastRequestTime = LocalDateTime.of(1970, 1, 1, 0, 0, 0)
            .atZone(ZoneId.systemDefault()).toLocalDateTime();

    public SspDevice(String device_id) {
        this.device_id = device_id;
    }

    public boolean isInAdsHourBlock() {
        if (ads_hour_block == null || ads_hour_block.isEmpty()) {
            return true;
        }

        int hourMinute = Util.getHourMinuteNow();
        for (AdsHourBlock block : ads_hour_block) {
            if (block.getStartHourMinute() <= hourMinute && block.getEndHourMinute() >= hourMinute) {
                return true;
            }
        }

        return false;
    }

    public List<HourlyMultiplier> getHourMultiplier() {
        return multiplier;
    }

    public int getMultiplier() {
        return getMultiplier(LocalDateTime.now());
    }

    /**
     * Lấy multiplier dựa trên thời gian cụ thể
     * @param dateTime Thời gian cần lấy multiplier
     * @return Giá trị multiplier tương ứng
     */
    public int getMultiplier(LocalDateTime dateTime) {
        if (multiplier == null || multiplier.isEmpty()) {
            return 0;
        }
        
        int hour = dateTime.getHour();
        String dayOfWeek = getDayOfWeekLowerCase(dateTime);
        
        if (hour >= 0 && hour < multiplier.size()) {
            HourlyMultiplier hourlyMultiplier = multiplier.get(hour);
            return hourlyMultiplier.getMultiplierForDay(dayOfWeek);
        }
        
        return 0;
    }

    private String getDayOfWeekLowerCase(LocalDateTime dateTime) {
        String day = dateTime.getDayOfWeek().name().toLowerCase();
        return day.substring(0, 3); // Trả về "mon", "tue", "wed", ...
    }


}
