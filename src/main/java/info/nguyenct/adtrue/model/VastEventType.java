package info.nguyenct.adtrue.model;

public enum VastEventType {
    VAST_REQUEST(0),
    IMPRESSION(1),
    CREATIVE_VIEW(2),
    START(3),
    FIRST_QUARTILE(4),
    MIDPOINT(5),
    THIRD_QUARTILE(6),
    COMPLETE(7),
    CLICK_THROUGH(8),
    ERROR(9),
    UNKNOWN(-1);

    private final int value;

    VastEventType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static VastEventType fromCode(String code) {
        try {
            int codeInt = Integer.parseInt(code);
            for (VastEventType type : values()) {
                if (type.value == codeInt) {
                    return type;
                }
            }
        } catch (NumberFormatException e) {
            // Ignore
        }
        return UNKNOWN;
    }

    public String getEventName() {
        switch (this) {
            case VAST_REQUEST: return "vastRequest";
            case IMPRESSION: return "impression";
            case CREATIVE_VIEW: return "creativeView";
            case START: return "start";
            case FIRST_QUARTILE: return "firstQuartile";
            case MIDPOINT: return "midpoint";
            case THIRD_QUARTILE: return "thirdQuartile";
            case COMPLETE: return "complete";
            case CLICK_THROUGH: return "clickThrough";
            case ERROR: return "error";
            default: return "unknown";
        }
    }

    public static VastEventType fromEventName(String eventName) {
        if (eventName == null) return UNKNOWN;
    
        switch (eventName.toLowerCase()) {
            case "start":
                return START;
            case "firstquartile":
                return FIRST_QUARTILE;
            case "midpoint":
                return MIDPOINT;
            case "thirdquartile":
                return THIRD_QUARTILE;
            case "complete":
                return COMPLETE;
            case "creativeview":
                return CREATIVE_VIEW;
            case "error":
                return ERROR;
            case "impression":
                return IMPRESSION;
            case "clickthrough":
                return CLICK_THROUGH;
            case "vastrequest":
                return VAST_REQUEST;
            default:
                return UNKNOWN;
        }
    }
}