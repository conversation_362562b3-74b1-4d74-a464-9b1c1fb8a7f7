package info.nguyenct.adtrue.service;

import info.nguyenct.adtrue.model.SspDevice;
import info.nguyenct.adtrue.model.VastReport;
import info.nguyenct.adtrue.repository.JdbcVastReportRepository;
import info.nguyenct.adtrue.util.SspDeviceCsvImporter;
import info.nguyenct.adtrue.util.Util;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Service để insert VastReport mới vào database mỗi 5 phút một lần
 */
@Service
public class VastReportInsertService {
    
    private static final Logger logger = LoggerFactory.getLogger(VastReportInsertService.class);
    
    private final JdbcVastReportRepository vastReportRepository;
    private static final Random random = new Random();

    private List<SspDevice> devices = new ArrayList<>();
    private final LocalDateTime endCampaign = LocalDateTime.of(2025, 9, 11, 22,0,0);
    private LocalTime startTime = LocalTime.of(8, 0);
    private LocalTime endTime = LocalTime.of(22, 0);
    private static final double REDUCE_RATE = 0.03d;

    public VastReportInsertService(JdbcVastReportRepository vastReportRepository) {
        this.vastReportRepository = vastReportRepository;
    }

    @PostConstruct
    public void initialize() throws IOException {
        this.devices = SspDeviceCsvImporter.processCsvFile("coke-ooh.csv");
//        insertFirstData();
        logger.info("VastReportInsertService initialized");
    }

    public void insertFirstData() {
        LocalDateTime start = LocalDateTime.of(2025, 8, 7, 8,0,0);
        LocalDateTime now = LocalDateTime.now();
        while (start.isBefore(now)) {
            if (start.toLocalTime().isAfter(startTime) && start.toLocalTime().isBefore(endTime)) {
                List<VastReport> reports = generateVastReports(start);
                // Sử dụng JdbcVastReportRepository để insert batch
                vastReportRepository.batchSave(reports);
                logger.info("Inserted {} VastReport records for time {}", reports.size(), start);
            }
            start = start.plusMinutes(5);
        }
    }

    /**
     * Scheduled task chạy mỗi 5 phút để insert VastReport mới vào database
     */
    @Scheduled(cron = "0 */5 * * * *")
    public void insertVastReports() {
        logger.info("Starting scheduled VastReport insertion");
        
        try {
            // Tạo danh sách VastReport mẫu
            LocalDateTime now = LocalDateTime.now();
            if (now.isAfter(endCampaign) || now.toLocalTime().isBefore(startTime) || now.toLocalTime().isAfter(endTime)) {
                logger.info("Current time is before start time, skipping insertion");
                return;
            }

            //lấy tròn 5ph
            LocalDateTime baseTime = now.withMinute((now.getMinute()/5)*5).withSecond(0);
            List<VastReport> reports = generateVastReports(baseTime);
            
            if (!reports.isEmpty()) {
                // Sử dụng JdbcVastReportRepository để insert batch
                vastReportRepository.batchSave(reports);
                logger.info("Successfully inserted {} VastReport records into database", reports.size());
            } else {
                logger.warn("No VastReport records to insert");
            }
            
        } catch (Exception e) {
            logger.error("Error occurred while inserting VastReport records: {}", e.getMessage(), e);
        }
        
        logger.info("Completed scheduled VastReport insertion");
    }
    
    /**
     * Tạo danh sách VastReport mẫu để insert vào database
     * @return List<VastReport> danh sách VastReport mẫu
     */
    private List<VastReport> generateVastReports(LocalDateTime time) {
        time = time.withMinute((time.getMinute()/5)*5).withSecond(0);

        int reportNo = (int) java.time.Duration.between(startTime, time).toMinutes() / 5;
        List<VastReport> reports = new ArrayList<>();

        for (SspDevice device : devices) {
            VastReport vastReport = createVastReport(device, "cm", "1001201", "1990017", time, 130/168.0f, reportNo);
            reports.add(vastReport);
        }
        logger.debug("Generated {} sample VastReport records", reports.size());
        return reports;
    }
    
    /**
     * Tạo một VastReport mẫu với dữ liệu ngẫu nhiên
     * @param baseTime thời gian cơ sở để tạo eventDate
     * @return VastReport đối tượng VastReport mẫu
     */
    private VastReport createVastReport(SspDevice sspDevice, String ssp, String campaignId, String bannerId, LocalDateTime baseTime, double spotRate, int reportNo) {
        VastReport report = new VastReport();
        
        // Thông tin cơ bản
        report.setDeviceId(sspDevice.getDevice_id());
        report.setSspName(ssp);
        report.setCampaignId(campaignId);
        report.setBannerId(bannerId);
        
        report.setEventDate(baseTime);
        report.setTime(baseTime);
        
        report.setChannelId(sspDevice.getChannel_id());
        report.setVendorId(sspDevice.getVendor_id());
        report.setRegionId(sspDevice.getRegion_id());
        report.setCityId(sspDevice.getCity_id());
        report.setDistrictId(sspDevice.getDistrict_id());
        report.setWardId(sspDevice.getWard_id());
        report.setStoreId(sspDevice.getStore_id());
        report.setMultiplier(sspDevice.getMultiplier());

        int spot = getSpotByReportNo(spotRate, reportNo);

        report.setTotalVastRequest(getReportValueWithReduceRate(spot + Util.getRandomInt(spot)));
        // Impression thường nhỏ hơn hoặc bằng vast requests
        report.setTotalImpression(getReportValueWithReduceRate(spot));
        report.setTotalCreativeView(getReportValueWithReduceRate(spot));
        report.setTotalStart(getReportValueWithReduceRate(spot));
        report.setTotalFirstQuartile(getReportValueWithReduceRate(spot));
        report.setTotalMidpoint(getReportValueWithReduceRate(spot));
        report.setTotalThirdQuartile(getReportValueWithReduceRate(spot));
        report.setTotalComplete(getReportValueWithReduceRate(spot));
        
        // Click through và error
        report.setTotalClickThrough(0);
        report.setTotalError(0); // 0-4 errors
        // Duration (giây) - trung bình 15-30 giây cho mỗi complete
        report.setTotalDuration(0);
        
        return report;
    }

    public static int getSpotByReportNo(double spotRate, int reportNo) {
        return (int) (Math.floor(spotRate * reportNo) - Math.floor(spotRate*(reportNo-1)));
    }

    public static int getReportValueWithReduceRate(int spot) {
        if (random.nextDouble() < REDUCE_RATE) {
            return 0;
        }
        return spot;
    }
    
    /**
     * Phương thức để insert VastReport thủ công (có thể gọi từ API hoặc test)
     * @param reports danh sách VastReport cần insert
     */
    public void insertVastReports(List<VastReport> reports) {
        if (reports == null || reports.isEmpty()) {
            logger.warn("No VastReport records provided for insertion");
            return;
        }
        
        try {
            vastReportRepository.batchSave(reports);
            logger.info("Manually inserted {} VastReport records into database", reports.size());
        } catch (Exception e) {
            logger.error("Error occurred while manually inserting VastReport records: {}", e.getMessage(), e);
            throw e;
        }
    }

    public static void main(String[] args) {
        int sum = 0;
        int sumWithReduce = 0;
        for (int i = 0; i < 168; i++) {
            int spot = getSpotByReportNo(130.0d/168, i);
            sum += spot;
            sumWithReduce += getReportValueWithReduceRate(spot);
            System.out.println(spot);
        }
        System.out.println(sum + " " + sumWithReduce);
    }
}
