package info.nguyenct.adtrue.service;

import info.nguyenct.adtrue.model.CampaignUrlWhitelist;
import info.nguyenct.adtrue.model.MediaInfo;
import info.nguyenct.adtrue.repository.CampaignUrlWhitelistRepository;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class CampaignUrlWhitelistService {
    
    private final CampaignUrlWhitelistRepository repository;
    private final CreativeCachingService creativeCachingService;

    public CampaignUrlWhitelistService(CampaignUrlWhitelistRepository repository, 
                                     CreativeCachingService creativeCachingService) {
        this.repository = repository;
        this.creativeCachingService = creativeCachingService;
    }
    
    public List<CampaignUrlWhitelist> getWhitelistByCampaign(String campaignId) {
        return repository.findByCampaignId(campaignId);
    }

    public Map<String, String> getWhitelistUrlsByCampaign(String campaignId) {
        Map<String, String> result = new HashMap<>();
        List<CampaignUrlWhitelist> whitelist = getWhitelistByCampaign(campaignId);
        for (CampaignUrlWhitelist entry : whitelist) {
            result.put(entry.getUrl(), entry.getMediaPath());
        }
        return result;
    }
    
    public List<CampaignUrlWhitelist> updateWhitelist(String campaignId, List<String> urls, String baseUrl) {
        // Lấy tất cả các url trong whitelist của campaign này
        List<CampaignUrlWhitelist> existingWhitelist = repository.findByCampaignId(campaignId);
        
        // Tìm các URL cần xóa (có trong DB nhưng không có trong danh sách mới)
        List<String> urlsToDelete = existingWhitelist.stream()
                .map(CampaignUrlWhitelist::getUrl)
                .filter(existingUrl -> !urls.contains(existingUrl))
                .toList();
        
        // Xóa các URL không còn trong danh sách
        if (!urlsToDelete.isEmpty()) {
            repository.deleteUrls(campaignId, urlsToDelete);
        }
        
        // Tìm các URL cần thêm mới (có trong danh sách mới nhưng không có trong DB)
        List<String> existingUrls = existingWhitelist.stream()
                .map(CampaignUrlWhitelist::getUrl)
                .toList();
        List<String> urlsToAdd = urls.stream()
                .filter(url -> !existingUrls.contains(url))
                .toList();
        
        // Thêm mới các URL và thực hiện creative caching
        if (!urlsToAdd.isEmpty()) {
            // Thực hiện creative caching cho từng URL và lấy media path
            for (String url : urlsToAdd) {
                String mediaPath = null;
                creativeCachingService.cacheCreative(url, 15, "/viettel/tvc","");
            }
        }
        
        return repository.findByCampaignId(campaignId);
    }
    
    public List<CampaignUrlWhitelist> updateWhitelist(String campaignId, List<String> urls) {
        return updateWhitelist(campaignId, urls, null);
    }
    
    public boolean isUrlAllowed(String campaignId, String url) {
        return repository.existsByCampaignIdAndUrl(campaignId, url);
    }
}