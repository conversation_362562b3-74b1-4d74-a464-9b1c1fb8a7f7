package info.nguyenct.adtrue.service;

import info.nguyenct.adtrue.model.MediaInfo;
import info.nguyenct.adtrue.util.FileUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static info.nguyenct.adtrue.util.FileUtil.*;

@Service
public class CreativeCachingService {
    private static final Logger logger = LoggerFactory.getLogger(CreativeCachingService.class);

    // ExecutorService với 4 thread để giới hạn số lượng tiến trình ffmpeg trong creativeInfo
    private final ExecutorService ffmpegCreativeExecutor = Executors.newFixedThreadPool(4);

    @Value("${vistar.video.output_dir:/vistar_ads_video}")
    private String outputDirectory;

    @Value("${vistar.video.bitrate:200}")
    private int videoBitrate;

    @Value("${vistar.video.codec:libx264}")
    private String videoCodec;

    private final RedisService redisService;

    public CreativeCachingService(RedisService redisService) {
        this.redisService = redisService;
    }

    public MediaInfo getCreativeCachedInfo(String assetUrl) {
        return redisService.getCacheMedia(assetUrl);
    }

    public void cacheCreative(String assertUrl, int duration) {
        cacheCreative(assertUrl, duration, null, null);
    }

    public void cacheCreative(String assertUrl, int duration, String baseDir, String baseUrl) {
        if (assertUrl == null || assertUrl.isEmpty()) {
            logger.error("Asset URL is null or empty");
            return;
        }
        MediaInfo cachedMedia = redisService.getCacheMedia(assertUrl);
        if (cachedMedia != null) {
            return;
        }

        final String lockKey = "creative_cache_lock:" + assertUrl;
        final String lockValue = String.valueOf(System.nanoTime());
        if (redisService.lock(lockKey, lockValue, 300)) {
            String outputDir = baseDir != null ? baseDir : LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String outputUrl = baseUrl != null ? baseUrl : "";
            ffmpegCreativeExecutor.submit(() -> {
                try {
                    Path outputPath = prepareOutputDirectory(outputDir);
                    Path mediaPath = processCacheCreative(assertUrl, outputPath, duration);
                    String mediaUrl = outputUrl + outputDir + "/" + mediaPath.getFileName().toString();
                    int bitrate = extractBitrateFromVideo(mediaPath);
                    redisService.saveCacheMedia(assertUrl, mediaUrl, bitrate);
                    logger.info("Cached creative: url {}, mediaPath {}, bitrate {}", assertUrl, mediaPath, bitrate);
                } catch (IOException | InterruptedException e) {
                    logger.error("Error caching creative in realtime: {}", e.getMessage());
                } finally {
                    redisService.releaseLock(lockKey, lockValue);
                }
            });
            return;
        }
    }

    private Path processCacheCreative(String assetUrl, Path outputDir, int duration) throws IOException, InterruptedException {
        if (assetUrl == null || assetUrl.isEmpty()) {
            logger.error("Asset URL is null or empty");
            return null;
        }

        Path downloadedFile = downloadFile(assetUrl, outputDir);
        String extension = getExtensionFromPath(downloadedFile);
        if (".mp4".equals(extension)) {
            double framerate = extractFramerateFromVideo(downloadedFile);
            if (framerate > 24) {
                return processHighFramerateVideo(downloadedFile);
            } else {
                return downloadedFile;
            }
        }else {
            return processImageFile(downloadedFile, duration);
        }
    }

    private Path downloadFile(String url, Path outputDir) throws IOException {
        try {
            URL fileUrl = new URL(url);
            String filename = System.nanoTime() + "_" + FileUtil.getFilenameFromUrl(fileUrl);
            Path destination = outputDir.resolve(filename);
            Files.copy(fileUrl.openStream(), destination, StandardCopyOption.REPLACE_EXISTING);
            return destination;
        } catch (IOException e) {
            logger.error("Failed to download file from {}: {}", url, e.getMessage());
            throw e;
        }
    }

    private Path processImageFile(Path imagePath, int duration)
            throws IOException, InterruptedException {
        if (imagePath == null || !Files.exists(imagePath)) {
            logger.error("Image path is null or does not exist: {}", imagePath);
            return null;
        }

        String filename = getFilenameWithoutExtension(imagePath);
        Path videoPath = imagePath.getParent().resolve(filename + ".mp4");
        String ffmpegCommand = String.format(
                "ffmpeg -y -loop 1 -i \"%s\" -vf \"scale=min(1920\\,iw):min(1080\\,ih):force_original_aspect_ratio=decrease\" " +
                        "-c:v %s -r 24 -t %d -pix_fmt yuv420p -b:v %dk \"%s\"",
                imagePath.toAbsolutePath(),
                videoCodec,
                duration,
                videoBitrate,
                videoPath.toAbsolutePath()
        );

        ProcessResult result = runExternalCommand(ffmpegCommand);
        if (result.exitCode() == 0 && !result.output().toString().isEmpty()) {
            Files.deleteIfExists(imagePath);
            logger.debug("Deleted original media file after successful conversion: {}", imagePath);
            return videoPath;
        }
        return null;
    }

    private Path processHighFramerateVideo(Path videoPath) throws IOException, InterruptedException {
        if (videoPath == null || !Files.exists(videoPath)) {
            logger.error("Video path is null or does not exist: {}", videoPath);
            return null;
        }
        int mediaBitrate = extractBitrateFromVideo(videoPath);

        String filename = getFilenameWithoutExtension(videoPath);
        Path destination = videoPath.getParent().resolve(filename + "_24fps.mp4");

        String ffmpegCommand = String.format(
                "ffmpeg -y -i \"%s\" -c:v %s -r 24 -b:v %dk -pix_fmt yuv420p \"%s\"",
                videoPath.toAbsolutePath(),
                videoCodec,
                mediaBitrate / 1000,
                destination.toAbsolutePath()
        );

        ProcessResult result = runExternalCommand(ffmpegCommand);
        if (result.exitCode() == 0) {
            Files.deleteIfExists(videoPath);
            return destination;
        }
        return null;
    }

    private int extractBitrateFromVideo(Path videoPath) throws IOException, InterruptedException {
        String ffprobeCommand = String.format(
                "ffprobe -v error -select_streams v:0 -show_entries stream=bit_rate -of default=noprint_wrappers=1:nokey=1 \"%s\"",
                videoPath.toAbsolutePath()
        );

        ProcessResult result = runExternalCommand(ffprobeCommand);

        if (result.exitCode() == 0 && !result.output().toString().isEmpty()) {
            try {
                return Integer.parseInt(result.output().toString());
            } catch (NumberFormatException e) {
                logger.warn("Could not parse bitrate from ffprobe output: {}", result.output());
            }
        }

        // Return default bitrate if extraction fails
        return videoBitrate;
    }

    private double extractFramerateFromVideo(Path videoPath) throws IOException, InterruptedException {
        String ffprobeCommand = String.format(
                "ffprobe -v error -select_streams v:0 -show_entries stream=r_frame_rate -of default=noprint_wrappers=1:nokey=1 \"%s\"",
                videoPath.toAbsolutePath()
        );

        ProcessResult result = runExternalCommand(ffprobeCommand);

        if (result.exitCode() == 0 && !result.output().toString().isEmpty()) {
            try {
                String framerateStr = result.output().toString();
                // ffprobe trả về framerate dưới dạng phân số (ví dụ: 30000/1001)
                if (framerateStr.contains("/")) {
                    String[] parts = framerateStr.split("/");
                    double numerator = Double.parseDouble(parts[0]);
                    double denominator = Double.parseDouble(parts[1]);
                    return numerator / denominator;
                } else {
                    return Double.parseDouble(framerateStr);
                }
            } catch (NumberFormatException e) {
                logger.warn("Could not parse framerate from ffprobe output: {}", result.output());
            }
        }

        // Trả về giá trị mặc định nếu không thể xác định framerate
        return 25.0;
    }

    private Path prepareOutputDirectory(String dir) throws IOException {
        Path outputDir = Paths.get(outputDirectory, dir);
        if (!Files.exists(outputDir)) {
            Files.createDirectories(outputDir);
            logger.info("Created output directory: {}", outputDir.toAbsolutePath());
        }
        return outputDir;
    }

    private static ProcessResult runExternalCommand(String command) throws IOException, InterruptedException {
        logger.debug("Executing ffmpeg command: {}", command);

        ProcessBuilder processBuilder = new ProcessBuilder();
        if (System.getProperty("os.name").toLowerCase().contains("windows")) {
            processBuilder.command("cmd.exe", "/c", command);
        } else {
            processBuilder.command("sh", "-c", command);
        }

        processBuilder.redirectErrorStream(true);
        Process process = processBuilder.start();

        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line.trim());
            }
        }

        int exitCode = process.waitFor();
        ProcessResult result = new ProcessResult(output, exitCode);
        return result;
    }

    private record ProcessResult(StringBuilder output, int exitCode) {
    }

}