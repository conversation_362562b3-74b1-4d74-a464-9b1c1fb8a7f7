package info.nguyenct.adtrue.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import info.nguyenct.adtrue.model.AdsHourBlock;
import info.nguyenct.adtrue.model.SspDevice;
import info.nguyenct.adtrue.restclient.HttpGetClient;
import info.nguyenct.adtrue.restclient.HttpResult;
import info.nguyenct.adtrue.util.Util;
import info.nguyenct.adtrue.util.SspDeviceCsvImporter;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

@Service
public class SspDeviceManager {
    private static final Logger logger = LoggerFactory.getLogger(SspDeviceManager.class);

    @Value("${adtrue.api.sspdevice.url}")
    private String sspDeviceUrl;

    private Map<String, SspDevice> deviceIdMap = new HashMap<>();
    private Map<String, String> macToDeviceIdMap = new HashMap<>();
    private final HttpGetClient httpGetClient;

    public SspDeviceManager(MeterRegistry meterRegistry, ObjectMapper objectMapper) {
        this.httpGetClient = new HttpGetClient("ssp-device-fetch", meterRegistry, new Util.MyJsonDeserialize<>(objectMapper, SspDevice[].class));
    }

    @PostConstruct
    public void init() {
        logger.info("Initializing SspDeviceManager");
        refreshDevices();
    }

    @Scheduled(cron = "0 */10 * * * *") // 10 phút = 600,000 milliseconds
    public void refreshDevices() {
        logger.info("Refreshing SSP devices from API");
        
        // Sử dụng virtual thread để thực hiện request bất đồng bộ
        try {
            //winmart ssp device;
            List<SspDevice> devices = SspDeviceCsvImporter.processCsvFile("winmart_camp.csv");
            List<SspDevice> sspDevices = getSspDevices();
            devices.addAll(sspDevices);

            Map<String, SspDevice> newDeviceIdMap = new HashMap<>();
            Map<String, String> newMacToDeviceIdMap = new HashMap<>();

            // Cập nhật cache với dữ liệu mới
            for (SspDevice device : devices) {
                String deviceId = device.getDevice_id();

                // Lưu mapping MAC -> device_id nếu có MAC
                if (device.getMac() != null && !device.getMac().isEmpty()) {
                    newMacToDeviceIdMap.put(device.getMac(), deviceId);
                }

                for (AdsHourBlock block : device.getAds_hour_block()) {
                    try {
                        block.setStartHourMinute(Util.getHourMinute(block.getStart()));
                        block.setEndHourMinute(Util.getHourMinute(block.getEnd()));
                    } catch (Exception e) {
                        logger.error("Error parsing ads hour block: {} in ssp device: {} {}", block.toString(), device.getDevice_id(), e);
                    }
                }

                // Copy thời gian request cuối cùng từ cache cũ
                SspDevice oldDevice = getDeviceInfo(deviceId);
                if (oldDevice != null) {
                    device.setLastRequestTime(oldDevice.getLastRequestTime());
                    device.setCacheCreatives(oldDevice.isCacheCreatives());
                }
                newDeviceIdMap.put(deviceId, device);
            }

            // Cập nhật cache chính
            deviceIdMap = newDeviceIdMap;
            macToDeviceIdMap = newMacToDeviceIdMap;
            logger.info("Refreshed SSP devices from API. Total devices: {}", deviceIdMap.size());
        } catch (Exception e) {
            logger.error("Error refreshing SSP devices", e);
        }
    }

    public List<SspDevice> getSspDevices() {
        HttpResult<SspDevice[]> result = httpGetClient.sendGet(sspDeviceUrl);

        if (result.isSuccess()) {
            return Arrays.asList(result.value());
        }else {
            return List.of();
        }
    }

    public void updateDeviceHearBeat(String deviceId) {
        SspDevice device = getDeviceInfo(deviceId);
        if (device == null) {
            logger.warn("Device not found: {}", deviceId);
            return;
        }
        device.setLastRequestTime(LocalDateTime.now());
    }
    
    /**
     * Lấy thông tin SspDevice từ bộ nhớ
     * @param deviceId ID của thiết bị
     * @return Đối tượng SspDevice hoặc null nếu không tìm thấy
     */
    public SspDevice getDeviceInfo(String deviceId) {
        if (deviceId == null || deviceId.isEmpty()) {
            return null;
        }
        return deviceIdMap.get(deviceId);
    }
    
    /**
     * Lấy device_id từ MAC address
     * @param mac MAC address của thiết bị
     * @return Device ID hoặc null nếu không tìm thấy
     */
    public String getDeviceIdFromMac(String mac) {
        return macToDeviceIdMap.get(mac);
    }
    
    /**
     * Lấy tất cả thiết bị
     * @return Danh sách tất cả thiết bị
     */
    public List<SspDevice> getAllDevices() {
        return List.copyOf(deviceIdMap.values());
    }
}