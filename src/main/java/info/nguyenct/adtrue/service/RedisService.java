package info.nguyenct.adtrue.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import info.nguyenct.adtrue.model.AdtrueAd;
import info.nguyenct.adtrue.model.MediaInfo;
import info.nguyenct.adtrue.model.SspDeviceData;
import info.nguyenct.adtrue.util.Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.connection.ReturnType;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.time.LocalDateTime;

@Service
public class RedisService {
    private static final Logger logger = LoggerFactory.getLogger(RedisService.class);

    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;

    public RedisService(RedisTemplate<String, Object> redisTemplate, ObjectMapper objectMapper) {
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
    }

    public boolean lock(String key, String value, long timeoutSeconds) {
        try {
            Boolean result = redisTemplate.opsForValue().setIfAbsent(key, value, timeoutSeconds, TimeUnit.SECONDS);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            logger.error("Error acquiring lock for key {}: {}", key, e.getMessage(), e);
            return false;
        }
    }

    public boolean releaseLock(String key, String value) {
        try {
            String script = 
                "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                "    return redis.call('del', KEYS[1]) " +
                "else " +
                "    return 0 " +
                "end";
            
            Long result = redisTemplate.execute(
                (RedisCallback<Long>) connection -> 
                    connection.eval(script.getBytes(), ReturnType.INTEGER, 1, 
                        key.getBytes(), value.getBytes())
            );
            
            return Long.valueOf(1).equals(result);
        } catch (Exception e) {
            logger.error("Error releasing lock for key {}: {}", key, e.getMessage(), e);
            return false;
        }
    }

    public void saveCMSpotTrackerTotalSpot(String deviceId, String bannerId, int totalSpot) {
        int monthDay = Util.getMonthDayNow();
        String key = "cm:spot_tracker:" + deviceId + ":" + bannerId + ":" + monthDay;
        redisTemplate.opsForHash().put(key, "totalSpot", String.valueOf(totalSpot));
        redisTemplate.expire(key, 24, TimeUnit.HOURS);
    }

    /**
     * Cập nhật thông tin SpotTracker của CM trong Redis
     * @param deviceId ID của thiết bị
     * @param bannerId ID của banner
     * @param numberOfSpotReturned Số lượng spot đã phát
     * @param lastSpotTimestamp Thời điểm phát spot cuối cùng
     */
    public void updateCMSpotTracker(String deviceId, String bannerId, int numberOfSpotReturned, long lastSpotTimestamp) {
        int monthDay = Util.getMonthDayNow();
        String key = "cm:spot_tracker:" + deviceId + ":" + bannerId + ":" + monthDay;
        
        redisTemplate.opsForHash().put(key, "numberOfSpotReturned", String.valueOf(numberOfSpotReturned));
        redisTemplate.opsForHash().put(key, "lastSpotTimestamp", String.valueOf(lastSpotTimestamp));
        redisTemplate.expire(key, 24, TimeUnit.HOURS);
    }

    /**
     * Lấy thông tin SpotTracker của CM từ Redis
     * @param deviceId ID của thiết bị
     * @param bannerId ID của banner
     * @return Map chứa thông tin SpotTracker
     */
    public Map<Object, Object> getCMSpotTracker(String deviceId, String bannerId) {
        int monthDay = Util.getMonthDayNow();
        String key = "cm:spot_tracker:" + deviceId + ":" + bannerId + ":" + monthDay;
        return redisTemplate.opsForHash().entries(key);
    }

    public String getMappedAdId(String adId) {
        String key = "ad_id_mapping:" + adId;
        Object mappedAdId = redisTemplate.opsForValue().get(key);
        if (mappedAdId != null) {
            return mappedAdId.toString();
        }
        return null;
    }

    /**
     * Save ad start timestamps to Redis with 24-hour expiry
     * @param adStartTimestamps Map of adId to start timestamp
     */
    public void saveAdStartTimestamps(Map<String, Long> adStartTimestamps) {
        if (adStartTimestamps == null || adStartTimestamps.isEmpty()) {
            return;
        }
        
        String key = "vast:ad_start_timestamps";
        
        // Delete existing key first
        redisTemplate.delete(key);
        
        // Save all values as hash entries
        List adIds = new ArrayList<>(adStartTimestamps.keySet());
        Map<String, String> redisMap = new HashMap<>();
        for (Map.Entry<String, Long> entry : adStartTimestamps.entrySet()) {
            adIds.add(entry.getKey());
            redisMap.put(entry.getKey(), String.valueOf(entry.getValue()));
        }
        
        redisTemplate.opsForHash().putAll(key, redisMap);
         redisTemplate.expire(key, 24, TimeUnit.HOURS);
        
        logger.debug("Saved {} ad start timestamps to Redis with 24-hour expiry", adStartTimestamps.size());
    }

    /**
     * Get all ad start timestamps from Redis
     * @return Map of adId to timestamp string
     */
    public Map<String, String> getAdStartTimestamps() {
        String key = "vast:ad_start_timestamps";
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
        
        Map<String, String> result = new HashMap<>();
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            result.put(entry.getKey().toString(), entry.getValue().toString());
        }
        
        return result;
    }

    /**
     * Get a unique ID by incrementing a counter in Redis
     * The counter is specific to the current month and day
     * @return The unique ID value
     */
    private Long getUniqueId(int monthDay) {
        // Create key with current month and day
        String key = "unique_id:" + monthDay;
        
        // Increment the counter (creates key with value 1 if it doesn't exist)
        Long uniqueId = redisTemplate.opsForValue().increment(key);
        
        // If returned value is 1, it means the key was just created
        if (uniqueId != null && uniqueId == 1) {
            redisTemplate.expire(key, 24, TimeUnit.HOURS);
        }
        
        return uniqueId;
    }

    public String getUniqueId(String sspPrefix) {
        LocalDate now = LocalDate.now();
        int monthDay = now.getMonthValue()*100 + now.getDayOfMonth();
        Long uniqueId = getUniqueId(monthDay);
        if (uniqueId == null) {
            uniqueId = 10_000_000L + System.currentTimeMillis()/10_000_000L;
        }
        return sspPrefix + monthDay + Long.toString(uniqueId, 36);
    }

    /**
     * Lưu metrics của Vistar vào Redis
     * @param metrics Map chứa các metrics cần lưu
     */
    public void saveVistarMetrics(Map<String, Integer> metrics) {
        try {
            String key = "vistar:metrics";
            String metricsJson = objectMapper.writeValueAsString(metrics);
            redisTemplate.opsForValue().set(key, metricsJson);
            redisTemplate.expire(key, 7, TimeUnit.DAYS); // Lưu trong 7 ngày
            
            // Lưu thêm timestamp
            String timestampKey = "vistar:metrics:last_updated";
            redisTemplate.opsForValue().set(timestampKey, LocalDateTime.now().toString());
            redisTemplate.expire(timestampKey, 7, TimeUnit.DAYS);
            
            logger.debug("Saved Vistar metrics to Redis");
        } catch (JsonProcessingException e) {
            logger.error("Error serializing Vistar metrics to JSON: {}", e.getMessage(), e);
        }
    }

    /**
     * Lấy metrics của Vistar từ Redis
     * @return Map chứa các metrics hoặc null nếu không tìm thấy
     */
    public Map<String, Integer> getVistarMetrics() {
        try {
            String key = "vistar:metrics";
            Object metricsJson = redisTemplate.opsForValue().get(key);
            
            if (metricsJson != null) {
                return objectMapper.readValue(metricsJson.toString(), 
                        new TypeReference<Map<String, Integer>>() {});
            }
            return null;
        } catch (Exception e) {
            logger.error("Error deserializing Vistar metrics from Redis: {}", e.getMessage(), e);
            return null;
        }
    }


    /**
     * Save AdsGettingWorkflow metrics to Redis
     * @param metrics Map of metrics to save
     */
    public void saveAdsWorkflowMetrics(Map<String, Integer> metrics) {
        try {
            String key = "adtrue:ads_workflow:metrics";
            String metricsJson = objectMapper.writeValueAsString(metrics);
            redisTemplate.opsForValue().set(key, metricsJson);
            redisTemplate.expire(key, 7, TimeUnit.DAYS); // Lưu trong 7 ngày
            
            // Lưu thêm timestamp
            String timestampKey = "adtrue:ads_workflow:metrics:last_updated";
            redisTemplate.opsForValue().set(timestampKey, LocalDateTime.now().toString());
            redisTemplate.expire(timestampKey, 7, TimeUnit.DAYS);
            
            logger.debug("Saved AdsGettingWorkflow metrics to Redis");
        } catch (JsonProcessingException e) {
            logger.error("Error serializing AdsGettingWorkflow metrics to JSON: {}", e.getMessage(), e);
        }
    }

    /**
     * Get AdsGettingWorkflow metrics from Redis
     * @return Map of metrics or null if not found
     */
    public Map<String, Integer> getAdsWorkflowMetrics() {
        try {
            String key = "adtrue:ads_workflow:metrics";
            Object metricsJson = redisTemplate.opsForValue().get(key);
            
            if (metricsJson != null) {
                return objectMapper.readValue(metricsJson.toString(), 
                        new TypeReference<Map<String, Integer>>() {});
            }
            return null;
        } catch (Exception e) {
            logger.error("Error deserializing AdsGettingWorkflow metrics from Redis: {}", e.getMessage(), e);
            return null;
        }
    }

    // Creative caching method
    public MediaInfo getCacheMedia(String assertUrl) {
        try {
            String key = "media_cache:" + assertUrl;
            Object value = redisTemplate.opsForValue().get(key);
            if (value != null) {
                return objectMapper.readValue(value.toString(), MediaInfo.class);
            }
        } catch (Exception e) {
            logger.error("Error deserializing media cache: {}", e.getMessage(), e);
        }
        return null;
    }

    public void saveCacheMedia(String assertUrl, String mediaUrl, int bitrate) {
        try {
            String key = "media_cache:" + assertUrl;
            String json = objectMapper.writeValueAsString(new MediaInfo(mediaUrl, bitrate));
            redisTemplate.opsForValue().set(key, json, 24, TimeUnit.HOURS);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public void removeCacheMedia(String assertUrl) {
        String key = "media_cache:" + assertUrl;
        redisTemplate.delete(key);
    }

    /**
     * Cache ad for a device with 15-minute expiry
     * @param deviceId Device ID
     * @param ad Ad to cache
     * @param minutes Minutes until expiry
     */
    public void cacheAd(String deviceId, AdtrueAd ad, int minutes) {
        try {
            String adJson = objectMapper.writeValueAsString(ad);
            String key = "ads_cache:" + deviceId;
            redisTemplate.opsForValue().set(key, adJson, minutes, TimeUnit.MINUTES);
            String adtrueKey = "adtrue:" + ad.getAdId();
            redisTemplate.opsForValue().set(adtrueKey, adJson, 24, TimeUnit.HOURS);
        } catch (JsonProcessingException e) {
            logger.error("Error serializing ad to JSON: {}", e.getMessage(), e);
        }
    }

    public void saveAdtrueAd(AdtrueAd ad) {
        try {
            String adJson = objectMapper.writeValueAsString(ad);
            String adtrueKey = "adtrue:" + ad.getAdId();
            redisTemplate.opsForValue().set(adtrueKey, adJson, 24, TimeUnit.HOURS);
        } catch (JsonProcessingException e) {
            logger.error("Error serializing ad to JSON: {}", e.getMessage(), e);
        }
    }

    public AdtrueAd getAdtrueAd(String adId) {
        try {
            String adtrueKey = "adtrue:" + adId;
            Object value = redisTemplate.opsForValue().get(adtrueKey);
            if (value != null) {
                return objectMapper.readValue(value.toString(), AdtrueAd.class);
            }
        } catch (Exception e) {
            logger.error("Error deserializing ad from JSON: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * Get cached ad for a device
     * @param deviceId Device ID
     * @return Cached ad or null if not found
     */
    public AdtrueAd getCachedAd(String deviceId) {
        try {
            String key = "ads_cache:" + deviceId;
            Object value = redisTemplate.opsForValue().get(key);
            if (value != null) {
                return objectMapper.readValue(value.toString(), AdtrueAd.class);
            }
        } catch (Exception e) {
            logger.error("Error deserializing ad from JSON: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * Remove ad from cache
     * @param deviceId Device ID
     */
    public void removeAdFromCache(String deviceId) {
        String key = "ads_cache:" + deviceId;
        redisTemplate.delete(key);
    }

    /**
     * Get size of ad cache
     * @return Number of ads in cache
     */
    public int getAdCacheSizeBySsp() {
        try {
            Set<String> keys = redisTemplate.keys("ads_cache:*");
            return keys != null ? keys.size() : 0;
        } catch (Exception e) {
            logger.error("Error getting ad cache size: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * Save ad to Redis with 24-hour expiry
     * @param adId Ad ID
     * @param ad Ad object to save
     */
    public <T> void saveAd(String adId, T ad) {
        try {
            String key = "ad:" + adId;
            String adJson = objectMapper.writeValueAsString(ad);
            redisTemplate.opsForValue().set(key, adJson, 24, TimeUnit.HOURS);
            logger.debug("Saved ad {} to Redis with 24-hour expiry", adId);
        } catch (JsonProcessingException e) {
            logger.error("Error serializing ad to JSON: {}", e.getMessage(), e);
        }
    }

    /**
     * Get ad from Redis
     * @param adId Ad ID
     * @param valueType Class of the ad object
     * @return Ad object or null if not found
     */
    public <T> T getAd(String adId, Class<T> valueType) {
        String key = "ad:" + adId;
        Object value = redisTemplate.opsForValue().get(key);
        if (value == null) {
            return null;
        }

        if (valueType == String.class) {
            return (T) value;
        }
        
        try {
            return objectMapper.readValue(value.toString(), valueType);
        } catch (JsonProcessingException e) {
            logger.error("Error deserializing ad from JSON: {}", e.getMessage(), e);
            return null;
        }
    }

    public SspDeviceData getDeviceData(String deviceId) {
        String key = "device_data:" + deviceId;
        Object json = redisTemplate.opsForValue().get(key);
        if (json == null) {
            return new SspDeviceData(deviceId);
        }
        
        try {
            return objectMapper.readValue(json.toString(), SspDeviceData.class);
        } catch (Exception e) {
            logger.error("Error deserializing device data: {}", e.getMessage(), e);
            return new SspDeviceData(deviceId);
        }
    }

    public void saveDeviceData(SspDeviceData deviceData, int expiryHours) {
        String key = "device_data:" + deviceData.getDeviceId();
        try {
            String json = objectMapper.writeValueAsString(deviceData);
            redisTemplate.opsForValue().set(key, json, expiryHours, TimeUnit.HOURS);
        } catch (Exception e) {
            logger.error("Error serializing device data: {}", e.getMessage(), e);
        }
    }
}