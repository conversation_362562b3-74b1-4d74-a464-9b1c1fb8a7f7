package info.nguyenct.adtrue.service;

import info.nguyenct.adtrue.model.SspDevice;
import info.nguyenct.adtrue.util.Util;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;

@Service
public class KafkaService {
    private static final Logger logger = LoggerFactory.getLogger(KafkaService.class);
    private final KafkaTemplate<String, String> kafkaTemplate;

    @Value("${kafka.topic.vistar_logging_topic}")
    private String vistarLoggingTopic;
    
    @Value("${kafka.topic.cm_logging_topic}")
    private String cmLoggingTopic;
    
    @Value("${kafka.topic.adtrue_logging_topic}")
    private String adtrueLoggingTopic;
    
    @Value("${kafka.topic.lmx.logging:lmx-logging-topic}")
    private String lmxLoggingTopic;
    
    @Value("${spring.kafka.server}")
    private String server;

    public KafkaService(KafkaTemplate<String, String> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    public boolean sendLogToSspTopic(String sspPrefix, String logData, long timestamp) {
        try {
            String log = logData + "^" + timestamp + "^" + Util.getTimeStampNow();
            String topic = getSspTopic(sspPrefix);
            
            // Gửi đến topic riêng của SSP
            kafkaTemplate.send(topic, log);
            
            logger.info("Sent vast log to Kafka topics {}: {}", sspPrefix, log);
            return true;
        } catch (Exception e) {
            throw new RuntimeException("Error sending vast logs to Kafka", e);
        }
    }
    
    private String getSspTopic(String sspPrefix) {
        if (sspPrefix.startsWith("vad_")) {
            return vistarLoggingTopic;
        } else if (sspPrefix.startsWith("cm_")) {
            return cmLoggingTopic;
        } else if (sspPrefix.startsWith("lmx_")){
            return lmxLoggingTopic;
        }else  if (sspPrefix.startsWith("modcart_")) {
            return getModcartLoggingTopic();
        } else {
            return adtrueLoggingTopic;
        }
    }

    public String getVistarLoggingTopic() {
        return vistarLoggingTopic;
    }

    public String getCmLoggingTopic() {
        return cmLoggingTopic;
    }

    public String getAdtrueLoggingTopic() {
        return adtrueLoggingTopic;
    }

    public String getLmxLoggingTopic() {
        return lmxLoggingTopic;
    }

    /**
     * Get Modcart logging topic name
     * @return Modcart logging topic name
     */
    public String getModcartLoggingTopic() {
        return "modcart-tracking-logs";
    }

    public boolean sendLogWithDevice(String sspPrefix, SspDevice device, String campaignId, String adId, int eventType, long timestamp) {
        try {
            // Build log data format: [campaign_id]^[banner_id]^[channelid]^[vendorid]^[regionid]^[cityid]^[districtid]^[wardid]^[storeid]^multiplier^[event_type]^[duration]^[ip]^[mac]^[deviceId]
            String logData = new StringBuilder()
                    .append(campaignId).append("^")
                    .append(adId).append("^")
                    .append(device.getChannel_id()).append("^")
                    .append(device.getVendor_id()).append("^")
                    .append(device.getRegion_id()).append("^")
                    .append(device.getCity_id()).append("^")
                    .append(device.getDistrict_id()).append("^")
                    .append(device.getWard_id()).append("^")
                    .append(device.getStore_id()).append("^")
                    .append(device.getMultiplier()).append("^")
                    .append(eventType).append("^")
                    .append("0").append("^") // duration
                    .append("[ip]").append("^") // ip
                    .append(device.getMac() != null ? device.getMac() : "").append("^")
                    .append(device.getDevice_id())
                    .toString();

            sendLogToSspTopic(sspPrefix, logData, timestamp);
            logger.info("Sent vast log to Kafka with event type {}: {}", eventType, logData);
            return true;
        } catch (Exception e) {
            throw new RuntimeException("Error sending vast logs to Kafka ssp "+ sspPrefix, e);
        }
    }

    // Giữ phương thức cũ để tương thích ngược
    public List<String> readAllVideoLogs(String consumerGroup) {
        return readLogs(consumerGroup, vistarLoggingTopic, cmLoggingTopic, adtrueLoggingTopic, lmxLoggingTopic, getModcartLoggingTopic());
    }

    public List<String> readLogs(String consumerGroupId, String... topic) {
        List<String> result = new ArrayList<>();

        // Create Kafka consumer properties
        Properties props = buildKafkaProperties(consumerGroupId);

        try (KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props)) {
            // Subscribe to the video logging topic
            consumer.subscribe(Arrays.asList(topic));

            // Thêm log để debug
            logger.info("Subscribed to topic: {}", String.join(",", topic));

            // Poll for records with longer timeout (10 seconds)
            ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(10));
            logger.info("Fetched {} records from Kafka topic {}", records.count(), topic);

            int count = 0;
            for (ConsumerRecord<String, String> record : records) {
                try {
                    // Thêm log để debug
                    result.add(record.value());
                    count++;

                    if (count >= 200000) {
                        break;
                    }
                } catch (Exception e) {
                    logger.error("Error deserializing record: {}", e.getMessage());
                }
            }

            // Commit offsets
            consumer.commitSync();
            logger.info("Committed offsets for {} records", count);

            return result;
        } catch (Exception e) {
            logger.error("Error reading from Kafka: {}", e.getMessage(), e);
            throw new RuntimeException("Error reading video logs from Kafka", e);
        }
    }

    private Properties buildKafkaProperties(String consumerGroupId) {
        Properties props = new Properties();
        props.put("bootstrap.servers", server);
        props.put("group.id", consumerGroupId);
        props.put("enable.auto.commit", "false");
        props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("isolation.level", "read_committed");
        props.put("auto.offset.reset", "earliest");

        props.put("max.poll.records", 200000);

        // Tăng giới hạn kích thước fetch (tùy chọn)
        props.put("fetch.max.bytes", 512*1024*1024); // 512MB
        props.put("max.partition.fetch.bytes", 128*1024*1024); // 128MB
        return props;
    }
}
