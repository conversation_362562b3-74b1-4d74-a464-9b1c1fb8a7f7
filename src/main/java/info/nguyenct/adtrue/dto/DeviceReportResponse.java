package info.nguyenct.adtrue.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceReportResponse {
    private String deviceId;
    private String dayHour;
    private long vastRequest;
    private long vastImpression;
    private long creativeView;
    private long start;
    private long firstQuartile;
    private long midpoint;
    private long thirdQuartile;
    private long complete;
    private long clickThrough;
    private long error;
    private long impression;
    private long reach;
    private int deviceCount;
    private int channelCount;
}
