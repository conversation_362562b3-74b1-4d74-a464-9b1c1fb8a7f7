package info.nguyenct.adtrue.ssp;

import info.nguyenct.adtrue.model.AdtrueAd;
import info.nguyenct.adtrue.model.SspDevice;
import reactor.core.publisher.Mono;

public interface Ssp {

    AdtrueAd requestAd(SspDevice device, long requestedTimestamp);

    /**
     * Get SSP identifier prefix used for ad IDs
     * @return SSP identifier prefix
     */
    String getSspPrefix();

    String sspName();
}