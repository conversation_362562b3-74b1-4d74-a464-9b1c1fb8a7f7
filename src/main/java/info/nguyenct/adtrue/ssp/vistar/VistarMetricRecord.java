package info.nguyenct.adtrue.ssp.vistar;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.Map;

@Data
public class VistarMetricRecord {
    private Long id;
    private LocalDateTime collectedAt = LocalDateTime.now();
    private int adsRequestsSent;
    private int adsResponseReceived;
    private int adsResponseStatusOk;
    private int adsResponseStatusNonOk;
    private int adsSlotRequests;
    private int adsSlotResponses;
    private int proofOfPlayCalls;
    private int proofOfPlayStatusOk;
    private int proofOfPlayStatusNonOk;
    private int pendingProofOfPlayCalls;
    private int deviceCount;
    private int adsRequestDurationP95;
    private int proofOfPlayDurationP95;
    private int creativeCacheDurationP95;

    public VistarMetricRecord() {
    }

    public VistarMetricRecord(Map<String, Integer> metrics) {
        this.collectedAt = LocalDateTime.now();
        this.adsRequestsSent = metrics.getOrDefault("ads_requests_sent", 0).intValue();
        this.adsResponseReceived = metrics.getOrDefault("ads_response_received", 0).intValue();
        this.adsResponseStatusOk = metrics.getOrDefault("ads_response_status_ok", 0).intValue();
        this.adsResponseStatusNonOk = metrics.getOrDefault("ads_response_status_non_ok", 0).intValue();
        this.adsSlotRequests = metrics.getOrDefault("ads_slot_requests", 0).intValue();
        this.adsSlotResponses = metrics.getOrDefault("ads_slot_responses", 0).intValue();
        this.proofOfPlayCalls = metrics.getOrDefault("proof_of_play_calls", 0).intValue();
        this.proofOfPlayStatusOk = metrics.getOrDefault("proof_of_play_status_ok", 0).intValue();
        this.proofOfPlayStatusNonOk = metrics.getOrDefault("proof_of_play_status_non_ok", 0).intValue();
        this.pendingProofOfPlayCalls = metrics.getOrDefault("pending_proof_of_play_calls", 0).intValue();
        this.deviceCount = metrics.getOrDefault("device_count", 0).intValue();
        this.adsRequestDurationP95 = metrics.getOrDefault("ads_request_duration_p95", 0).intValue();
        this.proofOfPlayDurationP95 = metrics.getOrDefault("proof_of_play_duration_p95", 0).intValue();
        this.creativeCacheDurationP95 = metrics.getOrDefault("creative_cache_duration_p95", 0).intValue();
    }
}