package info.nguyenct.adtrue.ssp.vistar;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class VistarAd {
    private String id;
    private String adTrueAdId;
    private String proof_of_play_url;
    private String expiration_url;
    private long lease_expiry;
    private String display_area_id;
    private String creative_id;
    private String asset_id;
    private String asset_url;
    private int width;
    private int height;
    private String mime_type;
    private int length_in_seconds;
    private int length_in_milliseconds;
    private long campaign_id;
    private String creative_category;
    private String advertiser;
    private String mediaPath; // URL tương đối để truy cập video qua Nginx
    private int bitrate; // Bitrate của video
}
