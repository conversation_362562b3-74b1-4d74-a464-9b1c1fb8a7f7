package info.nguyenct.adtrue.ssp.vistar;

import info.nguyenct.adtrue.model.AdtrueAd;
import info.nguyenct.adtrue.model.DisplayArea;
import info.nguyenct.adtrue.model.SspDevice;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

public class VistarHelper {
    private static final Logger logger = LoggerFactory.getLogger(VistarHelper.class);
    private static final String SSP_PREFIX = "vad_";

    /**
     * Convert VistarAd to AdtrueAd
     */
    public static AdtrueAd toAdtrueAd(VistarAd ad) {
        return AdtrueAd.builder()
                .adId(ad.getAdTrueAdId())
                .campaignId(String.valueOf(ad.getCampaign_id()))
                .assetUrl(ad.getAsset_url())
                .mediaPath(ad.getMediaPath())
                .width(String.valueOf(ad.getWidth()))
                .height(String.valueOf(ad.getHeight()))
                .bitrate(String.valueOf(ad.getBitrate()))
                .lengthInSeconds(String.valueOf(ad.getLength_in_seconds()))
                .mimeType("video/mp4")
                .sspPrefix(SSP_PREFIX)
                .build();
    }

    /**
     * Convert DisplayArea to VistarDisplayArea
     */
    public static VistarDisplayArea convertDisplayArea(DisplayArea area) {
        VistarDisplayArea adArea = new VistarDisplayArea();
        adArea.setId(area.getId());
        adArea.setWidth(area.getWidth());
        adArea.setHeight(area.getHeight());
        adArea.setMin_duration(area.getMin_duration());
        adArea.setMax_duration(area.getMax_duration());
        adArea.setStatic_duration(area.getMin_duration());
        adArea.setAllow_audio(area.getAllow_audio() == 1);

        // Parse file types from ads_file_type if available
        String[] fileTypes = {"image/jpeg", "image/png", "video/mp4"};
        adArea.setSupported_media(List.of(fileTypes));

        return adArea;
    }

    /**
     * Prepare request for Vistar API
     */
    public static VistarAdRequest prepareVistarRequest(SspDevice device, long displayTime, String networkId, String apiKey) {
        VistarAdRequest request = new VistarAdRequest();
        request.setDevice_id(device.getDevice_id());
        request.setDirect_connection(false);
        request.setDevice_attribute(List.of());
        request.setVenue_id(device.getVenue_id());
        request.setLatitude(device.getLatitude());
        request.setLongitude(device.getLongitude());
        request.setNetwork_id(networkId);
        request.setApi_key(apiKey);
        request.setDisplay_time(displayTime);

        // Convert display areas
        List<VistarDisplayArea> displayAreas = device.getDisplay_area().stream()
                .map(VistarHelper::convertDisplayArea)
                .collect(Collectors.toList());
        request.setDisplay_area(displayAreas);

        logger.debug("Prepared request to Vistar for device: {}", device.getDevice_id());
        return request;
    }

    /**
     * Get SSP prefix
     */
    public static String getSspPrefix() {
        return SSP_PREFIX;
    }
}