package info.nguyenct.adtrue.ssp.vistar;

import com.fasterxml.jackson.databind.ObjectMapper;
import info.nguyenct.adtrue.model.*;
import info.nguyenct.adtrue.restclient.HttpGetClient;
import info.nguyenct.adtrue.restclient.HttpPostJsonClient;
import info.nguyenct.adtrue.restclient.HttpResult;
import info.nguyenct.adtrue.service.CreativeCachingService;
import info.nguyenct.adtrue.service.KafkaService;
import info.nguyenct.adtrue.service.RedisService;
import info.nguyenct.adtrue.service.SspDeviceManager;
import info.nguyenct.adtrue.ssp.Ssp;
import info.nguyenct.adtrue.util.Util;
import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class VistarSsp implements Ssp {
    private static final Logger logger = LoggerFactory.getLogger(VistarSsp.class);
    private static final Logger vistarPayoutLogger = LoggerFactory.getLogger("vistar_ads_payout");
    
    private final RedisService redisService;
    private final CreativeCachingService creativeCachingService;
    private final KafkaService kafkaService;

    private final AtomicInteger adsSlotResponseCounter = new AtomicInteger(0);

    @Value("${vistar.get_ads.url}")
    private String getVistarAdsUrl;
    
    @Value("${vistar.networkid}")
    private String networkId;
    
    @Value("${vistar.api_key}")
    private String apiKey;

    @Value("${vistar.creative_cache.url}")
    private String vistarCreativeCacheUrl;

    private final SspDeviceManager sspDeviceManager;
    private final ExecutorService virtualThreadExecutor;

    private final HttpGetClient<String> proofOfPlayClient;
    private final HttpPostJsonClient<VistarAdResponse> getAdsClient;
    private final HttpPostJsonClient<VistarCreativeCacheResponse> creativeCacheClient;

    public VistarSsp(ObjectMapper objectMapper,
                     RedisService redisService, CreativeCachingService creativeCachingService, KafkaService kafkaService,
                     SspDeviceManager sspDeviceManager, ExecutorService virtualThreadExecutor,
                     MeterRegistry meterRegistry) {
        this.redisService = redisService;
        this.kafkaService = kafkaService;
        this.creativeCachingService = creativeCachingService;
        this.sspDeviceManager = sspDeviceManager;
        this.virtualThreadExecutor = virtualThreadExecutor;

        this.proofOfPlayClient = new HttpGetClient("vistar-payout", meterRegistry, Function.identity());
        this.getAdsClient = new HttpPostJsonClient<>("vistar-get-ad", meterRegistry, objectMapper, VistarAdResponse.class);
        this.creativeCacheClient = new HttpPostJsonClient<>("vistar-creative-cache", meterRegistry, objectMapper, VistarCreativeCacheResponse.class);
    }

    @Override
    public String getSspPrefix() {
        return VistarHelper.getSspPrefix();
    }

    @Override
    public String sspName() {
        return "vistar";
    }

    /**
     * Scheduled task để cache quảng cáo cho tất cả thiết bị mỗi 30 phút
     */
    @Scheduled(cron = "0 0 5 * * *")
    public void scheduledCreativeCaching() {
        logger.info("Starting schedule creative caching for all devices");
        for (SspDevice device : sspDeviceManager.getAllDevices()) {
            device.setCacheCreatives(false);
        }
    }

    @Scheduled(fixedDelay = 300000)
    public void checkCreativeCaching() {
        logger.info("Checking creative caching for all devices");
        try{
            List<SspDevice> devices = sspDeviceManager.getAllDevices();
            AtomicInteger cachedDevice = new AtomicInteger();

            for (SspDevice device : devices) {
                if (device.getVenue_id() == null || device.getVenue_id().isEmpty()) {
                    continue;
                }

                if (device.isCacheCreatives()) {
                    continue;
                }

                if (cacheCreativesForDevice(device) > 0) {
                    device.setCacheCreatives(true);
                    cachedDevice.incrementAndGet();
                }
            }

            logger.info("Completed creative caching check for {} devices, cached {} creatives total", devices.size(), cachedDevice.get());
        } catch (Exception e) {
            logger.error("Checking creative caching for all devices: {}", e.getMessage(), e);
        }
    }

    @Override
    public AdtrueAd requestAd(SspDevice device, long requestedTimestamp) {
        String deviceId = device.getDevice_id();
        logger.info("Requesting ad directly from Vistar API for device: {}", deviceId);

        // Chuẩn bị request
        VistarAdRequest request = VistarHelper.prepareVistarRequest(device, requestedTimestamp, networkId, apiKey);

        HttpResult<VistarAdResponse> result = getAdsClient.sendPost(getVistarAdsUrl, request);
        if (result.isSuccess()) {
            // Xử lý response
            VistarAdResponse vistarResponse = result.value();
            if (vistarResponse.getAdvertisement() != null && !vistarResponse.getAdvertisement().isEmpty()) {
                adsSlotResponseCounter.addAndGet(vistarResponse.getAdvertisement().size());

                // Lấy quảng cáo đầu tiên
                for (VistarAd ad: vistarResponse.getAdvertisement()) {
                    String adId = ad.getId();
                    if (!adId.startsWith(VistarHelper.getSspPrefix())) {
                        ad.setId(VistarHelper.getSspPrefix() + adId);
                    }

                    MediaInfo mediaInfo = creativeCachingService.getCreativeCachedInfo(ad.getAsset_url());
                    if (mediaInfo == null) {
                        logger.warn("Failed to get media path for ad {}, skipping", ad.getId());
                        continue;
                    }

                    ad.setMediaPath(mediaInfo.mediaUrl());
                    ad.setBitrate(mediaInfo.bitrate());
                    ad.setAdTrueAdId(redisService.getUniqueId(VistarHelper.getSspPrefix()));

                    redisService.saveAd(ad.getAdTrueAdId(), ad);
                    // Trả về AdtrueAd
                    return VistarHelper.toAdtrueAd(ad);
                }
            }
        }
        return null;
    }

    @Scheduled(fixedDelay = 5000)
    public void processPayoutFromKafka() {
        logger.info("Starting processing payout from Kafka for Vistar");
        
        List<String> logs = kafkaService.readLogs("vistar-payout-service", kafkaService.getVistarLoggingTopic());
        logger.info("Fetched {} logs from Vistar Kafka topic for payout processing", logs.size());
        
        for (String log : logs) {
            VastLogRequest logRequest = Util.fromLogString(log);
            if (logRequest == null) {
                logger.error("Failed to parse log: {}", log);
                continue;
            }

            String uniqueAdId = logRequest.getAdId();
            if (uniqueAdId == null || uniqueAdId.isEmpty()) {
                logger.warn("Received event with empty adId");
                continue;
            }

            // Verify this is a Vistar ad
            if (!uniqueAdId.startsWith(VistarHelper.getSspPrefix())) {
                logger.debug("Ignoring non-Vistar ad: {}", uniqueAdId);
                continue;
            }

            if (logRequest.getDeviceId() == null) {
                String deviceId = sspDeviceManager.getDeviceIdFromMac(logRequest.getMac());
                logRequest.setDeviceId(deviceId);
            }
            if (logRequest.getDeviceId() == null) {
                logger.warn("No device id found for mac: {}", logRequest.getMac());
                continue;
            }

            // Process payout for COMPLETE events
            if (logRequest.getEvent() == VastEventType.COMPLETE) {
                virtualThreadExecutor.submit(() -> {
                    // Get real ad from redis
                    VistarAd ad = redisService.getAd(uniqueAdId, VistarAd.class);
                    if (ad == null) {
                        logger.warn("No ad information found for payout: {}", uniqueAdId);
                        return;
                    }
                    boolean success = processPayoutAsync(ad, logRequest.getDeviceId());
                    if (!success) {
                        logger.warn("Failed to start payout for Vistar ad {}", ad.getId());
                    }
                });
            }
        }
        
        logger.info("Completed processing payout from Kafka for Vistar");
    }

    /**
     * Call Vistar proof of play URL asynchronously
     * @param ad Vistar ad that was viewed
     * @param deviceId Device ID that played the ad
     * @return Boolean indicating success/failure
     */
    private boolean processPayoutAsync(VistarAd ad, String deviceId) {
        // Lấy URL proof of play
        String proofOfPlayUrl = ad.getProof_of_play_url();
        if (proofOfPlayUrl == null || proofOfPlayUrl.isEmpty()) {
            vistarPayoutLogger.warn("No proof of play URL found for ad");
            return false;
        }

        MDC.put("adId", ad.getId());
        MDC.put("deviceId", deviceId);
        proofOfPlayClient.sendGet(proofOfPlayUrl);
        MDC.clear();
        return true;
    }

    /**
     * Get all metrics from this SSP
     * @return Map of metric name to metric value
     */
    public Map<String, Integer> getMetrics() {
        Map<String, Integer> metrics = new HashMap<>();
        
        // Get metrics from HTTP clients
        var getAdsMetrics = getAdsClient.getMetrics();
        var creativeCacheMetrics = creativeCacheClient.getMetrics();
        var proofOfPlayMetrics = proofOfPlayClient.getMetrics();
        
        // Add counter metrics
        metrics.put("ads_requests_sent", getAdsMetrics.requests());
        metrics.put("ads_response_received", getAdsMetrics.successResponses() + getAdsMetrics.errorResponses());
        metrics.put("ads_response_status_ok", getAdsMetrics.successResponses());
        metrics.put("ads_response_status_non_ok", getAdsMetrics.errorResponses());
        metrics.put("ads_slot_requests", getAdsMetrics.requests());
        metrics.put("ads_slot_responses", adsSlotResponseCounter.get());
        metrics.put("proof_of_play_calls", proofOfPlayMetrics.requests());
        metrics.put("proof_of_play_status_ok", proofOfPlayMetrics.successResponses());
        metrics.put("proof_of_play_status_non_ok", proofOfPlayMetrics.errorResponses());
        
        // Add gauge metrics
        metrics.put("pending_proof_of_play_calls", 0); // Update if you track pending calls
        metrics.put("device_count", sspDeviceManager.getAllDevices().size());
        
        // Add timer metrics (95th percentile in milliseconds)
        metrics.put("ads_request_duration_p95", getAdsMetrics.successDurationP95());
        metrics.put("proof_of_play_duration_p95", proofOfPlayMetrics.successDurationP95());
        metrics.put("creative_cache_duration_p95", creativeCacheMetrics.successDurationP95());
        
        return metrics;
    }

    /**
     * Gọi API creative caching của Vistar để cache quảng cáo trước
     * @param device Thiết bị cần cache quảng cáo
     * @return Số lượng quảng cáo đã cache thành công
     */
    public int cacheCreativesForDevice(SspDevice device) {
        MDC.put("deviceId", device.getDevice_id());
        logger.info("Caching creatives for device: {}", device.getDevice_id());
        // Chuẩn bị request
        long displayTime = Util.getTimeStampUTCNow() + 15;
        VistarCreativeCacheRequest request = new VistarCreativeCacheRequest();
        request.setDevice_id(device.getDevice_id());
        request.setDisplay_time(displayTime);
        request.setVenue_id(device.getVenue_id());
        request.setNetwork_id(networkId);
        request.setApi_key(apiKey);

        // Convert display areas
        List<VistarDisplayArea> displayAreas = device.getDisplay_area().stream()
                .map(VistarHelper::convertDisplayArea)
                .collect(Collectors.toList());
        request.setDisplay_area(displayAreas);

        HttpResult<VistarCreativeCacheResponse> result = creativeCacheClient.sendPost(vistarCreativeCacheUrl, request);

        if (!result.isSuccess()) {
            logger.error("Error caching creatives: HTTP {}", result.statusCode());
            MDC.clear();
            return 0;
        }

        VistarCreativeCacheResponse cacheResponse = result.value();

        // Add null check before iterating through assets
        if (cacheResponse.getAsset() == null) {
            logger.warn("No assets returned in creative cache response for device {}", device.getDevice_id());
            MDC.clear();
            return 0;
        }

        for (VistarCreativeCacheResponse.VistarCreative creative : cacheResponse.getAsset()) {
            creativeCachingService.cacheCreative(creative.getAsset_url(), creative.getLength_in_seconds());
        }

        logger.info("Successfully cached {} creatives for device {}",
                cacheResponse.getAsset().size(), device.getDevice_id());
        MDC.clear();
        return cacheResponse.getAsset().size();
    }
}
