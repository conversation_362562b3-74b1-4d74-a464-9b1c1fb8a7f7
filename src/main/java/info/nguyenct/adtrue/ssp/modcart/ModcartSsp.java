package info.nguyenct.adtrue.ssp.modcart;

import info.nguyenct.adtrue.service.CreativeCachingService;
import info.nguyenct.adtrue.service.KafkaService;
import info.nguyenct.adtrue.service.RedisService;
import info.nguyenct.adtrue.ssp.vast.VastSsp;
import info.nguyenct.adtrue.model.SspDevice;
import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.net.http.HttpClient;
import java.util.Map;

@Service
public class ModcartSsp extends VastSsp {
    private static final String SSP_PREFIX = "modcart_";

    public ModcartSsp(CreativeCachingService creativeCachingService,
                      RedisService redisService, KafkaService kafkaService,
                      MeterRegistry meterRegistry) {
        super(
            LoggerFactory.getLogger(ModcartSsp.class),
            creativeCachingService,
            redisService,
            kafkaService,
            meterRegistry,
            "modcart"
        );
    }

    @Override
    protected String getApiUrlFromDevice(SspDevice device) {
        return device.getModcart_url();
    }


    @Override
    protected String getLoggingTopic() {
        return kafkaService.getModcartLoggingTopic();
    }
}