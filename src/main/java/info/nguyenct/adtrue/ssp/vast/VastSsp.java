package info.nguyenct.adtrue.ssp.vast;

import info.nguyenct.adtrue.model.*;
import info.nguyenct.adtrue.restclient.HttpClientMetrics;
import info.nguyenct.adtrue.service.CreativeCachingService;
import info.nguyenct.adtrue.service.KafkaService;
import info.nguyenct.adtrue.service.RedisService;
import info.nguyenct.adtrue.ssp.Ssp;
import info.nguyenct.adtrue.restclient.HttpGetClient;
import info.nguyenct.adtrue.restclient.HttpResult;
import info.nguyenct.adtrue.util.Util;
import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.Logger;
import org.slf4j.MDC;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.function.Function;

public abstract class VastSsp implements Ssp {
    protected final Logger logger;

    protected final CreativeCachingService creativeCachingService;
    protected final RedisService redisService;
    protected final KafkaService kafkaService;
    protected final ExecutorService virtualThreadExecutor = Executors.newVirtualThreadPerTaskExecutor();

    protected final String sspPrefix;
    protected final String sspName;

    protected final HttpGetClient<VastAd> getAdClient;
    // Thêm client riêng cho từng loại event
    protected final HttpGetClient<String> trackingClient;

    protected VastSsp(
            Logger logger,
            CreativeCachingService creativeCachingService,
            RedisService redisService, 
            KafkaService kafkaService,
            MeterRegistry meterRegistry,
            String sspName) {
        
        this.logger = logger;
        this.creativeCachingService = creativeCachingService;
        this.redisService = redisService;
        this.kafkaService = kafkaService;
        this.sspName = sspName;
        this.sspPrefix = sspName+"_";

        this.getAdClient = new HttpGetClient<>(sspName + "-get-ad", meterRegistry, new VastParser());

        // Khởi tạo client riêng cho từng loại event
        this.trackingClient = new HttpGetClient<>(sspName + "-tracking", meterRegistry, Function.identity());
    }

    protected abstract String getApiUrlFromDevice(SspDevice device);

    protected abstract String getLoggingTopic();

    @Override
    public String getSspPrefix() {
        return sspPrefix;
    }

    @Override
    public String sspName() {
        return sspName;
    }

    @Override
    public AdtrueAd requestAd(SspDevice device, long requestedTimestamp) {
        String deviceId = device.getDevice_id();
        String apiUrl = getApiUrlFromDevice(device);

        return getVastAdFromUrl(deviceId, apiUrl);
    }

    public AdtrueAd getVastAdFromUrl(String deviceId, String apiUrl) {
        if (apiUrl == null || apiUrl.isEmpty()) {
            logger.debug("No {} URL configured for device: {}", sspName(), deviceId);
            return null;
        }

        logger.info("Requesting ad from {} API for device: {}", sspName(), deviceId);

        HttpResult<VastAd> result = getAdClient.sendGet(apiUrl);
        if (result.isSuccess()) {
            VastAd vastAd = result.value();
            if (vastAd!=null && vastAd.getAdId() != null) {
                // Get the primary media file (video/mp4 preferred)
                VastAd.MediaFile primaryMediaFile = vastAd.getVideoMediaFile();
                if (primaryMediaFile == null) {
                    logger.warn("No media file found for ad {}, skipping", vastAd.getAdId());
                    return null;
                }

                MediaInfo mediaInfo = creativeCachingService.getCreativeCachedInfo(primaryMediaFile.getUrl());
                if (mediaInfo == null) {
                    logger.warn("Failed to get media path for ad {}, skipping", vastAd.getAdId());
                    return null;
                }

                // Update the primary media file with local path and bitrate
                primaryMediaFile.setPath(mediaInfo.mediaUrl());
                primaryMediaFile.setBitrate(mediaInfo.bitrate());
                vastAd.setAdTrueAdId(redisService.getUniqueId(getSspPrefix()));
                redisService.saveAd(vastAd.getAdTrueAdId(), vastAd);

                return toAdtrueAd(vastAd, primaryMediaFile);
            }
        }
        return null;
    }

    protected AdtrueAd toAdtrueAd(VastAd vastAd, VastAd.MediaFile mediaFile) {
        return AdtrueAd.builder()
                .adId(vastAd.getAdTrueAdId())
                .campaignId(vastAd.getCampaignId())
                .sspPrefix(getSspPrefix())
                .mediaPath(mediaFile.getPath())
                .bitrate(String.valueOf(mediaFile.getBitrate()))
                .lengthInSeconds(String.valueOf(vastAd.getDuration()))
                .width(String.valueOf(mediaFile.getWidth()))
                .height(String.valueOf(mediaFile.getHeight()))
                .mimeType(mediaFile.getType())
                .build();
    }
    
    @Scheduled(fixedDelay = 5000)
    public void processTrackingFromKafka() {
        logger.info("Starting processing tracking from Kafka for {}", sspName());
        
        List<String> logs = kafkaService.readLogs(sspName() + "-tracking-service", getLoggingTopic());
        logger.info("Fetched {} logs from {} Kafka topic for tracking processing", logs.size(), sspName());
        
        for (String log : logs) {
            try {
                VastLogRequest logRequest = Util.fromLogString(log);
                if (logRequest == null) {
                    logger.error("Failed to parse log: {}", log);
                    continue;
                }

                String uniqueAdId = logRequest.getAdId();
                if (uniqueAdId == null || uniqueAdId.isEmpty()) {
                    logger.warn("Received event with empty adId");
                    continue;
                }

                // Verify this is a correct SSP ad
                if (!uniqueAdId.startsWith(getSspPrefix())) {
                    logger.debug("Ignoring non-{} ad: {}", sspName(), uniqueAdId);
                    continue;
                }

                if (logRequest.getDeviceId() == null) {
                    logger.warn("No device id found for mac: {}", logRequest.getMac());
                    continue;
                }
//                logger.info("Processing tracking for for device {} uniqueAdId {} for event {}", logRequest.getDeviceId(), uniqueAdId, logRequest.getEvent());
                VastAd vastAd = redisService.getAd(uniqueAdId, VastAd.class);

                if (vastAd == null) {
                    logger.warn("No ad information found for tracking: {}", uniqueAdId);
                    return;
                }
                // Process tracking asynchronously
                virtualThreadExecutor.submit(() -> processTracking(vastAd, logRequest.getDeviceId(), logRequest.getEvent(), uniqueAdId));
            } catch (Exception e) {
                logger.error("Error processing log: {}", log, e);
            }
        }
        
        logger.info("Completed processing tracking from Kafka for {}", sspName());
    }
    
    protected void processTracking(VastAd vastAd, String deviceId, VastEventType event, String uniqueAdId) {
        if (event == VastEventType.VAST_REQUEST) {
            return;
        }

        logger.info("Processing tracking for uniqueAdId {}, ad {} for device {} for event {}", uniqueAdId, vastAd.getAdId(), deviceId, event);

        List<String> trackingUrls = null;

        switch (event) {
            case START:
                trackingUrls = vastAd.getStartUrls();
                break;
            case FIRST_QUARTILE:
                trackingUrls = vastAd.getFirstQuartileUrls();
                break;
            case MIDPOINT:
                trackingUrls = vastAd.getMidpointUrls();
                break;
            case THIRD_QUARTILE:
                trackingUrls = vastAd.getThirdQuartileUrls();
                break;
            case COMPLETE:
                trackingUrls = vastAd.getCompleteUrls();
                break;
            case CREATIVE_VIEW:
                trackingUrls = vastAd.getCreativeViewUrls();
                break;
            case IMPRESSION:
                trackingUrls = vastAd.getImpressionUrls();
                break;
            case CLICK_THROUGH:
                trackingUrls = List.of(vastAd.getClickThroughUrl());
                break;
            case ERROR:
                trackingUrls = vastAd.getErrorUrls();
                break;
            case VAST_REQUEST:
                break;
            default:
                logger.warn("Unknown event type: {}", event);
                return;
        }

        if (trackingUrls == null || trackingUrls.isEmpty()) {
            logger.warn("No tracking URL found for event {}, ad {}", event, vastAd.getAdId());
            return;
        }
        logger.info("Sending tracking for ad {} for device {} for event {}", vastAd.getAdId(), deviceId, event);
        MDC.put("deviceId", deviceId);
        MDC.put("adId", uniqueAdId+"_"+vastAd.getAdId());
        MDC.put("eventType", event.getEventName());
        for (String trackingUrl : trackingUrls) {
            trackingClient.sendGet(trackingUrl);
        }
        MDC.clear();
    }
    
    /**
     * Get current metrics as a record
     * @return VastSspMetrics containing metrics data
     */
    public VastSspMetrics getMetrics() {
        HttpClientMetrics getAdMetrics = getAdClient.getMetrics();
        HttpClientMetrics trackingMetrics = trackingClient.getMetrics();

        return new VastSspMetrics(
            redisService.getAdCacheSizeBySsp(),
            // GetAdClient metrics
            getAdMetrics.requests(),
            getAdMetrics.successResponses(),
            getAdMetrics.errorResponses(),
            getAdMetrics.successDurationP95(),
            getAdMetrics.failureDurationP95(),
            // Individual event metrics
            trackingMetrics.requests(),
            trackingMetrics.successResponses(),
            trackingMetrics.errorResponses(),
            trackingMetrics.successDurationP95(),
            trackingMetrics.failureDurationP95()
        );
    }
}