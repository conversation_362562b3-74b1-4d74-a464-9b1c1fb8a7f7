package info.nguyenct.adtrue.ssp.vast;

import info.nguyenct.adtrue.model.VastEventType;
import lombok.Data;
import java.util.List;
import java.util.ArrayList;

@Data
public class VastAd {
    private String adId;
    private String adTrueAdId;
    private String campaignId;
    private List<String> impressionUrls = new ArrayList<>();
    private int duration;
    private List<MediaFile> mediaFiles = new ArrayList<>();
    private String adSystem;
    private String adTitle;
    
    // Replace single URLs with Lists
    private List<String> errorUrls = new ArrayList<>();
    private String clickThroughUrl; // Usually single URL
    private List<String> startUrls = new ArrayList<>();
    private List<String> firstQuartileUrls = new ArrayList<>();
    private List<String> midpointUrls = new ArrayList<>();
    private List<String> thirdQuartileUrls = new ArrayList<>();
    private List<String> completeUrls = new ArrayList<>();
    private List<String> creativeViewUrls = new ArrayList<>();

    // Add helper methods for tracking URLs
    public void addTrackingUrl(String event, String url) {
        if (url == null || url.isEmpty() || event == null) {
            return;
        }
        
        VastEventType eventType = VastEventType.fromEventName(event);
        if (eventType == VastEventType.UNKNOWN) {
            return;
        }
        
        switch (eventType) {
            case START:
                startUrls.add(url);
                break;
            case FIRST_QUARTILE:
                firstQuartileUrls.add(url);
                break;
            case MIDPOINT:
                midpointUrls.add(url);
                break;
            case THIRD_QUARTILE:
                thirdQuartileUrls.add(url);
                break;
            case COMPLETE:
                completeUrls.add(url);
                break;
            case CREATIVE_VIEW:
                creativeViewUrls.add(url);
                break;
            case ERROR:
                errorUrls.add(url);
                break;
            case IMPRESSION:
                impressionUrls.add(url);
                break;
        }
    }

    @Data
    public static class MediaFile {
        private String url;        // Nội dung của thẻ MediaFile
        private String path;       // Đường dẫn local sau khi tải về
        private String delivery;   // Thuộc tính delivery="progressive"
        private String type;       // Thuộc tính type="video/mp4"
        private int bitrate;       // Thuộc tính bitrate="500"
        private int width;         // Thuộc tính width="640"
        private int height;        // Thuộc tính height="480"
        private boolean scalable;  // Thuộc tính scalable="true"
        private boolean maintainAspectRatio; // Thuộc tính maintainAspectRatio="true"
        private String id;         // Thuộc tính id nếu có
    }

    public String getCampaignId() {
        if (campaignId == null || campaignId.isEmpty()) {
            return "camp-unknown";
        }
        return campaignId;
    }
    
    // Phương thức tiện ích để thêm impressionUrl
    public void addImpressionUrl(String url) {
        if (url != null && !url.isEmpty()) {
            impressionUrls.add(url);
        }
    }
    
    // Phương thức tiện ích để thêm mediaFile
    public void addMediaFile(MediaFile mediaFile) {
        if (mediaFile != null) {
            mediaFiles.add(mediaFile);
        }
    }
    
    // Phương thức tiện ích để lấy impressionUrl đầu tiên
    public String getImpressionUrl() {
        return impressionUrls.isEmpty() ? null : impressionUrls.get(0);
    }

    // Phương thức tiện ích để lấy MediaFile đầu tiên
    public MediaFile getFirstMediaFile() {
        return mediaFiles.isEmpty() ? null : mediaFiles.get(0);
    }
    
    // Get video file match with device
    public MediaFile getVideoMediaFile() {
        for (MediaFile mediaFile : mediaFiles) {
            if ("video/mp4".equals(mediaFile.getType())) {
                return mediaFile;
            }
        }
        return getFirstMediaFile();
    }

    public List<String> getTrackingUrls(VastEventType eventType) {
        if (eventType == null) {
            return new ArrayList<>();
        }
        
        switch (eventType) {
            case IMPRESSION:
                return impressionUrls;
            case ERROR:
                return errorUrls;
            case START:
                return startUrls;
            case FIRST_QUARTILE:
                return firstQuartileUrls;
            case MIDPOINT:
                return midpointUrls;
            case THIRD_QUARTILE:
                return thirdQuartileUrls;
            case COMPLETE:
                return completeUrls;
            case CREATIVE_VIEW:
                return creativeViewUrls;
            default:
                return new ArrayList<>();
        }
    }
}