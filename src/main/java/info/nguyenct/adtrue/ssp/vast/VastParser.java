package info.nguyenct.adtrue.ssp.vast;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.function.Function;

/**
 * Parser for LMX VAST XML responses
 */
public class VastParser implements Function<String, VastAd> {
    private final Logger logger = LoggerFactory.getLogger(VastParser.class);
    
    /**
     * Parse VAST XML and extract ad information
     * @param vastXml The VAST XML string to parse
     * @return LmxAd object containing the parsed ad information, or null if parsing fails
     */
    public VastAd apply(String vastXml) {
        if (vastXml == null || vastXml.trim().isEmpty()) {
            logger.error("Empty or null VAST XML provided");
            throw new IllegalArgumentException("Empty or null VAST XML provided");
        }
        
        // Kiểm tra xem XML có hợp lệ không
        if (!vastXml.contains("<VAST") || !vastXml.contains("</VAST>")) {
            logger.error("Invalid VAST XML format: Missing VAST tags");
            throw new IllegalArgumentException("Invalid VAST XML format: Missing VAST tags");
        }
        
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            // Tắt các tính năng có thể gây ra lỗ hổng bảo mật
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
            factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new ByteArrayInputStream(vastXml.getBytes("UTF-8")));
            
            VastAd vastAd = new VastAd();
            
            // Extract Ad ID
            NodeList adElements = doc.getElementsByTagName("Ad");
            if (adElements.getLength() > 0) {
                Element adElement = (Element) adElements.item(0);
                vastAd.setAdId(adElement.getAttribute("id"));
            }

            // Extract AdSystem
            NodeList adSystemElements = doc.getElementsByTagName("AdSystem");
            if (adSystemElements.getLength() > 0) {
                vastAd.setAdSystem(adSystemElements.item(0).getTextContent().trim());
            }

            // Extract AdTitle
            NodeList adTitleElements = doc.getElementsByTagName("AdTitle");
            if (adTitleElements.getLength() > 0) {
                vastAd.setAdTitle(adTitleElements.item(0).getTextContent().trim());
            }

            // Extract Error URL
            NodeList errorElements = doc.getElementsByTagName("Error");
            if (errorElements.getLength() > 0) {
                vastAd.addTrackingUrl("error",errorElements.item(0).getTextContent().trim());
            }

            // Extract ClickThrough URL
            NodeList clickThroughElements = doc.getElementsByTagName("ClickThrough");
            if (clickThroughElements.getLength() > 0) {
                vastAd.setClickThroughUrl(clickThroughElements.item(0).getTextContent().trim());
            }
            
            // Extract Campaign ID from Extensions/AdvertisingMetadata
            NodeList campaignIdElements = doc.getElementsByTagName("CampaignID");
            if (campaignIdElements.getLength() > 0) {
                String campaignId = campaignIdElements.item(0).getTextContent().trim();
                vastAd.setCampaignId(campaignId);
                logger.info("Extracted Campaign ID: {}", campaignId);
            }
            
            // Extract Impression URLs
            NodeList impressionElements = doc.getElementsByTagName("Impression");
            for (int i = 0; i < impressionElements.getLength(); i++) {
                String impressionUrl = impressionElements.item(i).getTextContent().trim();
                vastAd.addImpressionUrl(impressionUrl);
            }
            
            // Extract Media files
            NodeList mediaFileElements = doc.getElementsByTagName("MediaFile");
            for (int i = 0; i < mediaFileElements.getLength(); i++) {
                Element mediaFileElement = (Element) mediaFileElements.item(i);

                VastAd.MediaFile mediaFile = new VastAd.MediaFile();
                mediaFile.setUrl(mediaFileElement.getTextContent().trim());
                mediaFile.setDelivery(mediaFileElement.getAttribute("delivery"));
                mediaFile.setType(mediaFileElement.getAttribute("type"));

                // Parse numeric attributes safely
                try {
                    String bitrateStr = mediaFileElement.getAttribute("bitrate");
                    if (!bitrateStr.isEmpty()) {
                        mediaFile.setBitrate(Integer.parseInt(bitrateStr));
                    }
                } catch (NumberFormatException e) {
                    logger.warn("Invalid bitrate value: {}", mediaFileElement.getAttribute("bitrate"));
                }

                try {
                    String widthStr = mediaFileElement.getAttribute("width");
                    if (!widthStr.isEmpty()) {
                        mediaFile.setWidth(Integer.parseInt(widthStr));
                    }
                } catch (NumberFormatException e) {
                    logger.warn("Invalid width value: {}", mediaFileElement.getAttribute("width"));
                }

                try {
                    String heightStr = mediaFileElement.getAttribute("height");
                    if (!heightStr.isEmpty()) {
                        mediaFile.setHeight(Integer.parseInt(heightStr));
                    }
                } catch (NumberFormatException e) {
                    logger.warn("Invalid height value: {}", mediaFileElement.getAttribute("height"));
                }

                // Parse boolean attributes
                String scalableStr = mediaFileElement.getAttribute("scalable");
                if (!scalableStr.isEmpty()) {
                    mediaFile.setScalable("true".equalsIgnoreCase(scalableStr));
                }

                String maintainAspectRatioStr = mediaFileElement.getAttribute("maintainAspectRatio");
                if (!maintainAspectRatioStr.isEmpty()) {
                    mediaFile.setMaintainAspectRatio("true".equalsIgnoreCase(maintainAspectRatioStr));
                }

                mediaFile.setId(mediaFileElement.getAttribute("id"));

                vastAd.addMediaFile(mediaFile);
            }
            
            // Extract Duration
            NodeList durationElements = doc.getElementsByTagName("Duration");
            if (durationElements.getLength() > 0) {
                String durationStr = durationElements.item(0).getTextContent();
                vastAd.setDuration(parseDuration(durationStr));
            }
            
            // Extract Tracking URLs
            NodeList trackingElements = doc.getElementsByTagName("Tracking");
            for (int i = 0; i < trackingElements.getLength(); i++) {
                Element trackingElement = (Element) trackingElements.item(i);
                String event = trackingElement.getAttribute("event");
                String url = trackingElement.getTextContent().trim();
                vastAd.addTrackingUrl(event, url);
            }

            return vastAd;
        } catch (ParserConfigurationException | IOException | SAXException e) {
            logger.error("Error parsing VAST XML: {}", e.getMessage(), e);
            throw new RuntimeException("Error parsing VAST XML: " + e.getMessage(), e);
        }
    }
    
    /**
     * Parse duration string in format HH:MM:SS to seconds
     * @param durationStr Duration string in format HH:MM:SS
     * @return Duration in seconds
     */
    private int parseDuration(String durationStr) {
        try {
            // Format: HH:MM:SS
            String[] parts = durationStr.split(":");
            int hours = Integer.parseInt(parts[0]);
            int minutes = Integer.parseInt(parts[1]);
            int seconds = Integer.parseInt(parts[2]);
            
            return hours * 3600 + minutes * 60 + seconds;
        } catch (Exception e) {
            logger.error("Error parsing duration: {}", durationStr, e);
            return 0;
        }
    }
}