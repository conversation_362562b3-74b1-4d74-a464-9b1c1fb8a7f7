package info.nguyenct.adtrue.ssp;

import info.nguyenct.adtrue.ssp.cm.CMSsp;
import info.nguyenct.adtrue.ssp.lmx.LmxSsp;
import info.nguyenct.adtrue.ssp.modcart.ModcartSsp;
import info.nguyenct.adtrue.ssp.vistar.VistarSsp;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SspManager {
    private final List<Ssp> ssps;

    public SspManager(VistarSsp vistarSsp, AdtrueSsp adtrueSsp, CMSsp cmSsp, LmxSsp lmxSsp, ModcartSsp modcartSsp) {
        this.ssps = List.of(vistarSsp, adtrueSsp, cmSsp, lmxSsp, modcartSsp);
    }
    
    public Ssp getSspForAd(String adId) {
        for (Ssp ssp : ssps) {
            if (adId.startsWith(ssp.getSspPrefix())) {
                return ssp;
            }
        }

        return null;
    }
}