package info.nguyenct.adtrue.ssp;

import info.nguyenct.adtrue.model.AdtrueAd;
import info.nguyenct.adtrue.model.SspDevice;
import info.nguyenct.adtrue.service.RedisService;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import reactor.core.publisher.Mono;

@Service
public class AdtrueSsp implements Ssp {
    private static final Logger logger = LoggerFactory.getLogger(AdtrueSsp.class);
    private static final String SSP_PREFIX = "adt_";
    
    @Value("${adtrue.ads.json.path:classpath:adtrue_ads.json}")
    private String adtrueAdsJsonPath;

    private final RedisService redisService;
    
    private final ObjectMapper objectMapper;
    private List<AdtrueAd> defaultAds = new ArrayList<>();
    private final Random random = new Random();

    public AdtrueSsp(ObjectMapper objectMapper, RedisService redisService) {
        this.objectMapper = objectMapper;
        this.redisService = redisService;
    }

    @PostConstruct
    public void init() {
        logger.info("Initializing AdtrueSsp");
        loadAdsFromJson();
    }
    
    private void loadAdsFromJson() {
        try {
            Resource resource;
            if (adtrueAdsJsonPath.startsWith("classpath:")) {
                resource = new ClassPathResource(adtrueAdsJsonPath.substring("classpath:".length()));
            } else {
                resource = new FileSystemResource(adtrueAdsJsonPath);
            }

            List<AdtrueAd> ads = objectMapper.readValue(
                resource.getInputStream(),
                objectMapper.getTypeFactory().constructCollectionType(List.class, AdtrueAd.class)
            );
            this.defaultAds = ads;
            logger.info("Loaded {} ads from Adtrue JSON file: {}", defaultAds.size(), adtrueAdsJsonPath);
        } catch (Exception e) {
            logger.error("Error loading Adtrue ads from {}: {}", adtrueAdsJsonPath, e.getMessage(), e);
        }
    }
    
    public void reloadAds() {
        logger.info("Reloading Adtrue ads from JSON file");
        loadAdsFromJson();
    }

    @Override
    public AdtrueAd requestAd(SspDevice device, long requestedTimestamp) {
        AdtrueAd ads = defaultAds.get(random.nextInt(defaultAds.size()));
        ads.setAdId(redisService.getUniqueId(getSspPrefix()));
        return ads;
    }

    @Override
    public String getSspPrefix() {
        return SSP_PREFIX;
    }

    @Override
    public String sspName() {
        return "adtrue";
    }
}