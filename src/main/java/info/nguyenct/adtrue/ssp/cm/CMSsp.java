package info.nguyenct.adtrue.ssp.cm;

import com.fasterxml.jackson.databind.ObjectMapper;
import info.nguyenct.adtrue.model.AdtrueAd;
import info.nguyenct.adtrue.model.SspDevice;
import info.nguyenct.adtrue.service.RedisService;
import info.nguyenct.adtrue.ssp.Ssp;
import info.nguyenct.adtrue.restclient.HttpGetClient;
import info.nguyenct.adtrue.restclient.HttpResult;
import info.nguyenct.adtrue.util.Util;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class CMSsp implements Ssp {
    private static final Logger logger = LoggerFactory.getLogger(CMSsp.class);
    private static final String SSP_PREFIX = "cm_";
    
    @Value("${cm.api.url:https://cm.adtrue.com.vn/api/banner/booking}")
    private String cmApiUrl;
    
    @Value("${cm.api.token:nGFqyS6NBSCcx5XZ}")
    private String cmApiToken;

    private final HttpGetClient<CMAdResponse> httpGetClient;
    private final RedisService redisService;

    // Cache for device ads
    private Map<String, List<CMBanner>> deviceBannerMap = new ConcurrentHashMap<>();

    // Track spot delivery for each device and campaign
    public static class SpotTracker {
        String bannerId;
        int numberOfSpotReturned = 0;
        long lastSpotTimestamp = 0;
        int totalSpot;
        
        public SpotTracker(String bannerId, int totalSpot) {
            this.bannerId = bannerId;
            this.totalSpot = totalSpot;
        }
    }

    public CMSsp(MeterRegistry meterRegistry, ObjectMapper objectMapper,
                 RedisService redisService) {
        this.httpGetClient = new HttpGetClient("cm-get-ad", meterRegistry, new Util.MyJsonDeserialize<>(objectMapper, CMAdResponse.class));
        this.redisService = redisService;
    }

    @PostConstruct
    public void loadInventoryFromCM() {
        fetchAdsFromCM();
    }
    
    /**
     * Scheduled task to fetch ads from CM API every 5 minutes
     */
    @Scheduled(cron = "0 */5 * * * *")
    public void fetchAdsFromCM() {
        logger.info("Fetching ads from CM API");
        String url = cmApiUrl + "?token=" + cmApiToken;
        HttpResult<CMAdResponse> result = httpGetClient.sendGet(url);

        if (!result.isSuccess()) {
            return;
        }

        CMAdResponse cmResponse = result.value();
        if ("0".equals(cmResponse.getErr()) && cmResponse.getData() != null) {
            processAdsResponse(cmResponse.getData());
            logger.info("Successfully processed {} ads from CM API", cmResponse.getData().size());
        } else {
            logger.error("Error in CM API response: {}", cmResponse.getErrmsg());
        }

    }
    
    private void processAdsResponse(List<CMBanner> ads) {
        Map<String, List<CMBanner>> deviceBannerMapNew = new ConcurrentHashMap<>();

        for (CMBanner banner : ads) {
            //convert spot hour block to LocalTime
            for (CMSpotHourBlock block : banner.getSpotHourBlock()) {
                block.setStarTime(LocalTime.parse(block.getStart()));
                block.setEndTime(LocalTime.parse(block.getEnd()));
            }

            // Map ads to devices and initialize spot trackers
            for (CMDeviceTarget deviceTarget : banner.getDeviceTarget()) {
                String deviceId = deviceTarget.getDeviceId();
                deviceBannerMapNew.computeIfAbsent(deviceId, k -> new ArrayList<>()).add(banner);

                //tạo spot tracker, chỉ lưu total spot
                int totalSpot = Util.parseInt(deviceTarget.getSpot(), 0);
                redisService.saveCMSpotTrackerTotalSpot(deviceId, banner.getBannerId(), totalSpot);
            }
        }

        deviceBannerMap = deviceBannerMapNew;
    }
    
    @Override
    public AdtrueAd requestAd(SspDevice device, long requestedTimestamp) {
        String deviceId = device.getDevice_id();
        
        // Step 3: Check if device is targeted
        List<CMBanner> availableBanner = deviceBannerMap.get(deviceId);
        if (availableBanner == null || availableBanner.isEmpty()) {
            logger.debug("No banner available for device: {}", deviceId);
            return null;
        }

        // Get current time
        long currentTimestamp = Util.getTimeStampNow();
        LocalTime currentTime = LocalTime.now();

        for (CMBanner banner : availableBanner) {
            logger.debug("Checking banner: {}", banner.getBannerId());
            if (banner.getSpotHourBlock() == null || banner.getSpotHourBlock().isEmpty()) {
                continue;
            }

            String bannerId = banner.getBannerId();

            // Find the current active time block
            long secondsUntilEnd = 0;
            boolean isActive = false;
            for (CMSpotHourBlock block : banner.getSpotHourBlock()) {
                if (currentTime.isBefore(block.getStarTime())) {
                    secondsUntilEnd += ChronoUnit.SECONDS.between(block.getStarTime(), block.getEndTime());
                }else if (currentTime.isAfter(block.getStarTime()) && currentTime.isBefore(block.getEndTime())) {
                    isActive = true;
                    secondsUntilEnd += ChronoUnit.SECONDS.between(currentTime, block.getEndTime());
                }
            }

            if (!isActive) {
                logger.debug("Banner {} is not active at current time for device {}", bannerId, deviceId);
                continue;
            }

            // Get spot tracker for this device and banner from redis, if can not, create new one
            Map<Object, Object> trackerData = redisService.getCMSpotTracker(deviceId, bannerId);
            SpotTracker spotTracker = null;
            if (!trackerData.isEmpty()) {
                spotTracker = new SpotTracker(
                    bannerId,
                    Integer.parseInt(trackerData.getOrDefault("totalSpot", "0").toString())
                );
                spotTracker.numberOfSpotReturned = Integer.parseInt(
                        trackerData.getOrDefault("numberOfSpotReturned", "0").toString()
                );
                spotTracker.lastSpotTimestamp = Long.parseLong(
                    trackerData.getOrDefault("lastSpotTimestamp", "0").toString()
                );
            }else{
                int totalSpot = 0;
                for (CMDeviceTarget deviceTarget : banner.getDeviceTarget()) {
                    if (deviceTarget.getDeviceId().equals(deviceId)) {
                        totalSpot = Util.parseInt(deviceTarget.getSpot(),0);
                        break;
                    }
                }
                spotTracker = new SpotTracker(bannerId, totalSpot);
                redisService.saveCMSpotTrackerTotalSpot(deviceId, bannerId, totalSpot);
            }

            // Check if we've already delivered all spots
            if (spotTracker.numberOfSpotReturned >= spotTracker.totalSpot) {
                logger.debug("All spots already delivered for campaign {} on device {}", bannerId, deviceId);
                continue;
            }

            // Step 5: Calculate average time between spots
            int remainingSpots = spotTracker.totalSpot - spotTracker.numberOfSpotReturned;
            long averageSpotBetweenSeconds = secondsUntilEnd / Math.max(1, remainingSpots);

            // Step 6: Check if enough time has passed since last spot
            long timeSinceLastSpot = currentTimestamp - spotTracker.lastSpotTimestamp;
            if (timeSinceLastSpot >= averageSpotBetweenSeconds) {
                // Update tracker
                spotTracker.numberOfSpotReturned++;
                spotTracker.lastSpotTimestamp = currentTimestamp;
                
                // Lưu cập nhật vào Redis
                redisService.updateCMSpotTracker(deviceId, bannerId,
                    spotTracker.numberOfSpotReturned, spotTracker.lastSpotTimestamp);

                logger.info("Serving banner {} for device {}. Spot {}/{}, Time since last: {}s, Average interval: {}s",
                        bannerId, deviceId, spotTracker.numberOfSpotReturned, spotTracker.totalSpot,
                        timeSinceLastSpot, averageSpotBetweenSeconds);
                String adId = redisService.getUniqueId(getSspPrefix());
                redisService.saveAd(adId, banner.getBannerId());
                return fromCMBannerToAdtrueAd(banner, adId);
            }
        }
        
        // No suitable ad found
        return null;
    }
    
    private AdtrueAd fromCMBannerToAdtrueAd(CMBanner campaign, String adId) {
        CMAdCreative adCreative = campaign.getUrlTvc().get(0);
        return AdtrueAd.builder()
                .adId(adId)
                .sspPrefix(getSspPrefix())
                .campaignId(String.valueOf(campaign.getCampaignId()))
                .assetUrl(adCreative.getUrl())
                .mediaPath(adCreative.getUrl())
                .width(String.valueOf(adCreative.getWidth()))
                .height(String.valueOf(adCreative.getHeight()))
                .bitrate(String.valueOf(adCreative.getBitrate()))
                .lengthInSeconds(String.valueOf(adCreative.getDuration()))
                .mimeType(adCreative.getType())
                .build();
    }

    @Override
    public String getSspPrefix() {
        return SSP_PREFIX;
    }

    @Override
    public String sspName() {
        return "cm";
    }
}
