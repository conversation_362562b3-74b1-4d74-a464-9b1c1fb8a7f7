package info.nguyenct.adtrue.ssp.cm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CMDeviceTarget {
    @JsonProperty("vendor_id")
    private String vendorId;

    @JsonProperty("brand_id")
    private String brandId;

    @JsonProperty("region_id")
    private String regionId;

    @JsonProperty("city_id")
    private String cityId;

    @JsonProperty("district_id")
    private String districtId;

    @JsonProperty("ward_id")
    private String wardId;

    @JsonProperty("store_id")
    private String storeId;

    @JsonProperty("cm_did")
    private String cmDid;

    @JsonProperty("device_id")
    private String deviceId;

    private String spot;
}