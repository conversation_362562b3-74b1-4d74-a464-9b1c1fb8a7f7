package info.nguyenct.adtrue.ssp.cm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class CMBanner {
    @JsonProperty("ssp_id")
    private String sspId;

    @JsonProperty("banner_id")
    private String bannerId;

    @JsonProperty("campaign_id")
    private String campaignId;

    @JsonProperty("spot_hour_block")
    private List<CMSpotHourBlock> spotHourBlock;

    @JsonProperty("3rd_tracking")
    private CMThirdPartyTracking CMThirdPartyTracking;

    @JsonProperty("url_tvc")
    private List<CMAdCreative> urlTvc;

    @JsonProperty("device_target")
    private List<CMDeviceTarget> deviceTarget;
}