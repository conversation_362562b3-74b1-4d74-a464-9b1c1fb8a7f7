package info.nguyenct.adtrue.ssp.cm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class CMThirdPartyTracking {
    @JsonProperty("vastRequest")
    private List<CMTrackingUrl> vastRequest;
    
    @JsonProperty("impression")
    private List<CMTrackingUrl> impression;
    
    @JsonProperty("creativeView")
    private List<CMTrackingUrl> creativeView;
    
    private List<CMTrackingUrl> start;
    
    @JsonProperty("firstQuartile")
    private List<CMTrackingUrl> firstQuartile;
    
    @JsonProperty("midpoint")
    private List<CMTrackingUrl> midpoint;
    
    @JsonProperty("thirdQuartile")
    private List<CMTrackingUrl> thirdQuartile;
    
    @JsonProperty("complete")
    private List<CMTrackingUrl> complete;
    
    @JsonProperty("clickThrough")
    private List<CMTrackingUrl> clickThrough;
    
    @JsonProperty("error")
    private List<CMTrackingUrl> error;
}