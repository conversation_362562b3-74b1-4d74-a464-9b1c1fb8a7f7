package info.nguyenct.adtrue.ssp.lmx;

import info.nguyenct.adtrue.service.CreativeCachingService;
import info.nguyenct.adtrue.service.KafkaService;
import info.nguyenct.adtrue.service.RedisService;
import info.nguyenct.adtrue.ssp.vast.VastSsp;
import info.nguyenct.adtrue.model.SspDevice;
import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.net.http.HttpClient;
import java.util.Map;

@Service
public class LmxSsp extends VastSsp {
    public LmxSsp(CreativeCachingService creativeCachingService,
                  RedisService redisService, KafkaService kafkaService,
                  MeterRegistry meterRegistry) {
        super(
            LoggerFactory.getLogger(LmxSsp.class),
            creativeCachingService,
            redisService,
            kafkaService,
            meterRegistry,
            "lmx"
        );
    }

    @Override
    protected String getApiUrlFromDevice(SspDevice device) {
        return device.getLmx_url();
    }

    @Override
    protected String getLoggingTopic() {
        return kafkaService.getLmxLoggingTopic();
    }
}