package info.nguyenct.adtrue.interceptor;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.lang.reflect.Parameter;
import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component
public class ReportValidationInterceptor implements HandlerInterceptor {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        
        // Chỉ validate các endpoint trong CMReportController
        if (!handlerMethod.getBeanType().getSimpleName().equals("CMReportController")) {
            return true;
        }
        
        // Validate date range
        String fdateStr = request.getParameter("fdate");
        String tdateStr = request.getParameter("tdate");
        
        if (fdateStr != null && tdateStr != null) {
            try {
                LocalDate fdate = LocalDate.parse(fdateStr);
                LocalDate tdate = LocalDate.parse(tdateStr);
                
                if (fdate.isAfter(tdate)) {
                    sendErrorResponse(response, "Start date must be before or equal to end date");
                    return false;
                }
            } catch (DateTimeParseException e) {
                sendErrorResponse(response, "Invalid date format. Use YYYY-MM-DD");
                return false;
            }
        }
        
        // Validate integer parameters
        String[] intParams = {"channel_id", "vendor_id", "region_id", "city_id", "district_id", "ward_id", "store_id"};
        
        for (String param : intParams) {
            String value = request.getParameter(param);
            if (value != null && !value.isEmpty()) {
                try {
                    int intValue = Integer.parseInt(value);
                    if (intValue < 0) {
                        sendErrorResponse(response, param + " must be a non-negative integer");
                        return false;
                    }
                } catch (NumberFormatException e) {
                    sendErrorResponse(response, param + " must be a valid integer");
                    return false;
                }
            }
        }
        
        // Validate required parameters for detail endpoints
        if (request.getRequestURI().contains("/campaign/detail")) {
            if (isEmpty(request.getParameter("campaign_id"))) {
                sendErrorResponse(response, "Campaign ID is required");
                return false;
            }
            if (!validateGroupBy(request, response, "campaign")) {
                return false;
            }
        }
        
        if (request.getRequestURI().contains("/banner/detail")) {
            if (isEmpty(request.getParameter("banner_id"))) {
                sendErrorResponse(response, "Banner ID is required");
                return false;
            }
            if (!validateGroupBy(request, response, "banner")) {
                return false;
            }
        }
        
        return true;
    }
    
    private boolean validateGroupBy(HttpServletRequest request, HttpServletResponse response, String type) {
        String groupBy = request.getParameter("group_by");
        if (isEmpty(groupBy)) {
            sendErrorResponse(response, "group_by parameter is required");
            return false;
        }
        
        String[] validFields;
        if ("campaign".equals(type)) {
            validFields = new String[]{"day", "banner_id", "channel_id", "vendor_id", "region_id", 
                "city_id", "district_id", "ward_id", "store_id", "device_id"};
        } else {
            validFields = new String[]{"day", "channel_id", "vendor_id", "region_id", 
                "city_id", "district_id", "ward_id", "store_id", "device_id"};
        }
        
        boolean isValid = false;
        for (String field : validFields) {
            if (field.equals(groupBy)) {
                isValid = true;
                break;
            }
        }
        
        if (!isValid) {
            sendErrorResponse(response, "group_by must be one of: " + String.join(", ", validFields));
            return false;
        }
        
        return true;
    }
    
    private boolean isEmpty(String value) {
        return value == null || value.trim().isEmpty();
    }
    
    private void sendErrorResponse(HttpServletResponse response, String message) {
        try {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            response.setContentType("application/json");
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Invalid input parameters");
            errorResponse.put("message", message);
            
            response.getWriter().write(objectMapper.writeValueAsString(errorResponse));
        } catch (Exception e) {
            // Log exception
        }
    }
}