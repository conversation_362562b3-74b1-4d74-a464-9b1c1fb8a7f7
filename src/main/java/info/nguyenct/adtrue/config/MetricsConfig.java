package info.nguyenct.adtrue.config;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.prometheusmetrics.PrometheusConfig;
import io.micrometer.prometheusmetrics.PrometheusMeterRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.HttpOutputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.AbstractHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

@Configuration
public class MetricsConfig implements WebMvcConfigurer {

    @Bean
    @Primary
    public MeterRegistry meterRegistry() {
        return new PrometheusMeterRegistry(PrometheusConfig.DEFAULT);
    }
    
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        // Add custom converter for HashMap to OpenMetrics format
        converters.add(new MapToOpenMetricsConverter());
        
        // Add string converter with OpenMetrics support
        StringHttpMessageConverter converter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        converter.setSupportedMediaTypes(List.of(
            new MediaType("application", "openmetrics-text", StandardCharsets.UTF_8),
            new MediaType("text", "plain", StandardCharsets.UTF_8),
            MediaType.ALL
        ));
        converters.add(converter);
    }
    
    // Custom converter for HashMap to OpenMetrics format
    private static class MapToOpenMetricsConverter extends AbstractHttpMessageConverter<Map<?, ?>> {
        
        public MapToOpenMetricsConverter() {
            super(new MediaType("application", "openmetrics-text", StandardCharsets.UTF_8));
        }
        
        @Override
        protected boolean supports(Class<?> clazz) {
            return Map.class.isAssignableFrom(clazz);
        }
        
        @Override
        protected Map<?, ?> readInternal(Class<? extends Map<?, ?>> clazz, HttpInputMessage inputMessage) 
                throws IOException, HttpMessageNotReadableException {
            throw new HttpMessageNotReadableException("Reading maps not supported", inputMessage);
        }
        
        @Override
        protected void writeInternal(Map<?, ?> map, HttpOutputMessage outputMessage) 
                throws IOException, HttpMessageNotWritableException {
            StringBuilder builder = new StringBuilder();
            
            // Convert map to OpenMetrics format
            for (Map.Entry<?, ?> entry : map.entrySet()) {
                String key = entry.getKey().toString();
                String value = entry.getValue().toString();
                builder.append(key).append(" ").append(value).append("\n");
            }
            
            outputMessage.getBody().write(builder.toString().getBytes(StandardCharsets.UTF_8));
        }
    }
}