package info.nguyenct.adtrue.config;

import org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.ForwardedHeaderFilter;

@Configuration
public class UndertowConfig {

    @Bean
    public FilterRegistrationBean<ForwardedHeaderFilter> forwardedHeaderFilter() {
        FilterRegistrationBean<ForwardedHeaderFilter> filterRegistrationBean = new FilterRegistrationBean<>();
        filterRegistrationBean.setFilter(new ForwardedHeaderFilter());
        filterRegistrationBean.setOrder(0);
        return filterRegistrationBean;
    }

    @Bean
    public WebServerFactoryCustomizer<UndertowServletWebServerFactory> undertowCustomizer() {
        return factory -> {
            // <PERSON><PERSON><PERSON> h<PERSON>nh các tùy chọn server cho Undertow
            factory.addBuilderCustomizers(builder -> {
                builder.setServerOption(io.undertow.UndertowOptions.DECODE_URL, true);
                builder.setServerOption(io.undertow.UndertowOptions.ALLOW_UNESCAPED_CHARACTERS_IN_URL, true);
                builder.setServerOption(io.undertow.UndertowOptions.ALLOW_EQUALS_IN_COOKIE_VALUE, true);
                builder.setServerOption(io.undertow.UndertowOptions.ALLOW_ENCODED_SLASH, true);
                builder.setServerOption(io.undertow.UndertowOptions.MAX_PARAMETERS, 2000);
                builder.setServerOption(io.undertow.UndertowOptions.URL_CHARSET, "UTF-8");
                builder.setServerOption(io.undertow.UndertowOptions.ENABLE_RFC6265_COOKIE_VALIDATION, false);
            });

            // Add handler for invalid URLs and CONNECT requests
            factory.addDeploymentInfoCustomizers(deploymentInfo -> {
                deploymentInfo.addInitialHandlerChainWrapper(handler -> {
                    return exchange -> {
                        try {
                            if (exchange.getRequestMethod().toString().equals("CONNECT")) {
                                exchange.setStatusCode(403);
                                exchange.endExchange();
                                return;
                            }
                            handler.handleRequest(exchange);
                        } catch (IllegalArgumentException e) {
                            if (e.getMessage().contains("UT000068") || e.getMessage().contains("Servlet path match failed")) {
                                exchange.setStatusCode(400);
                                exchange.getResponseHeaders().put(io.undertow.util.Headers.CONTENT_TYPE, "text/plain");
                                exchange.getResponseSender().send("Bad Request: Invalid URL format");
                                return;
                            }
                            throw e;
                        }
                    };
                });
            });
        };
    }
}
