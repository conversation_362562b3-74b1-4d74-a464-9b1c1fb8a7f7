package info.nguyenct.adtrue.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        List<Server> servers = new ArrayList<>();
        
        // Sử dụng URL tương đối - sẽ tự động sử dụng domain của request hiện tại
        servers.add(new Server().url("/").description("Current domain"));

        return new OpenAPI()
                .servers(servers);
    }
}