package info.nguyenct.adtrue.config;

import info.nguyenct.adtrue.interceptor.ReportValidationInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    
    private final ReportValidationInterceptor reportValidationInterceptor;
    
    public WebMvcConfig(ReportValidationInterceptor reportValidationInterceptor) {
        this.reportValidationInterceptor = reportValidationInterceptor;
    }
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(reportValidationInterceptor)
                .addPathPatterns("/report/cm/**");
    }
}