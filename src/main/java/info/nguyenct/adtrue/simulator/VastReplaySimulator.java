package info.nguyenct.adtrue.simulator;

import info.nguyenct.adtrue.model.SspDevice;
import info.nguyenct.adtrue.model.VastEventType;
import info.nguyenct.adtrue.restclient.HttpClientMetrics;
import info.nguyenct.adtrue.util.Util;
import info.nguyenct.adtrue.util.SspDeviceCsvImporter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Simulator that replays VAST events from a log file
 */
public class VastReplaySimulator {
    private static final Logger logger = LoggerFactory.getLogger(VastReplaySimulator.class);
    private static final Random random = new Random();

    private final List<LogEntry> logEntries;
    private final String logFilePath;

    private final DeviceSimulator device;

    // Structure to hold log entries
    private static class LogEntry {
        long timestamp;
        VastEventType eventType;
        long eventTimestamp;

        public LogEntry(long timestamp, VastEventType eventType, long eventTimestamp) {
            this.timestamp = timestamp;
            this.eventType = eventType;
            this.eventTimestamp = eventTimestamp;
        }
    }
    
    /**
     * Constructor
     * 
     * @param logFilePath Path to the log file
     */
    public VastReplaySimulator(String logFilePath, DeviceSimulator device) throws IOException {
        this.logFilePath = logFilePath;
        this.logEntries = readLogEntries(logFilePath, random.nextFloat(0.01f, 0.04f));
        this.device = device;

        if (logEntries.isEmpty()) {
            logger.error("No valid log entries found for device {}", device.getDeviceId());
        }
    }
    
    /**
     * Start the replay process
     * 
     * @return True if replay was successful, false otherwise
     */
    public boolean replay() {
        if (logEntries.isEmpty()) {
            logger.error("No valid log entries found for device {}", device.getDeviceId());
            return false;
        }
        
        try {
            logger.info("Starting VAST event replay for device {} from {} logsize {}", device.getDeviceId(), logFilePath, logEntries.size());

            int startIndex = -1;
            while (startIndex<0 || startIndex > logEntries.size()) {
                startIndex = findClosestTimestampIndex(logEntries);
                if (startIndex < 0) {
                    logger.info("No valid log entries found for device {}", device.getDeviceId());
                    Thread.sleep(600000);
                }
            }

            int sleepTime = random.nextInt(60);
            Thread.sleep(sleepTime*1000);

            // Process log entries
            int totalEvents = 0;
            int successfulEvents = 0;

            for (int i = startIndex; i < logEntries.size()-1; i++) {
                LogEntry entry = logEntries.get(i);
                logger.info("Processing event: {} {} for device: {} at timestamp: {}", entry.timestamp, entry.eventType.getEventName(), device.getDeviceId(), entry.eventTimestamp);
                // Process the event
                device.processEvent(entry.eventType, Util.getTimeStampNow());
                totalEvents++;

                LogEntry nextEntry = logEntries.get(i+1);
                long diffMs = (nextEntry.timestamp - entry.timestamp) * 1000;  // Convert seconds to milliseconds
                if (diffMs > 0) {
                    logger.info("Waiting {}ms to simulate real timing", diffMs);
                    Thread.sleep(diffMs);
                }

            }

            logger.info("Replay completed for device {}. {}/{} events successful.", device.getDeviceId(), successfulEvents, totalEvents);
            return successfulEvents == totalEvents && totalEvents > 0;
            
        } catch (InterruptedException e) {
            logger.error("Error during replay: {}", e.getMessage(), e);
            return false;
        }
    }

    
    /**
     * Read all log entries from the file
     * 
     * @return List of log entries
     */
    private List<LogEntry> readLogEntries(String logFilePath, Float skipRate) throws IOException {
        List<LogEntry> entries = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(new FileReader(logFilePath))) {
            // Skip header line
            String line = reader.readLine();
            
            while ((line = reader.readLine()) != null) {
                String[] parts = line.split(",");
                if (parts.length < 4) {
                    logger.warn("Invalid log format: {}", line);
                    continue;
                }
                
                String timestampStr = parts[0];
                long timestamp = parseTimestamp(timestampStr);
                int eventTypeCode = Integer.parseInt(parts[2]);
                long eventTimestamp = Long.parseLong(parts[4]);

                // random skip
                if (random.nextFloat(1) < skipRate) {
                    continue;
                }

                VastEventType eventType = VastEventType.fromCode(String.valueOf(eventTypeCode));
                entries.add(new LogEntry(timestamp, eventType, eventTimestamp));
            }
        }
        
        return entries;
    }
    
    /**
     * Find the index of the log entry with timestamp closest to current time
     * 
     * @param entries List of log entries
     * @return Index of the closest entry
     */
    private int findClosestTimestampIndex(List<LogEntry> entries) {
        if (entries.isEmpty()) {
            return 0;
        }
        
        // Get current time in seconds since midnight
        LocalTime now = LocalTime.now();
        long currentTimeSeconds = now.toSecondOfDay();
        
        for (int i = 0; i < entries.size(); i++) {
            long entryTimeSeconds = entries.get(i).timestamp;
            if (entryTimeSeconds > currentTimeSeconds) {
                return i - 1;
            }
        }
        
        return -1;
    }
    

    /**
     * Parse a timestamp string in format HH:MM:SS to seconds since midnight
     * 
     * @param timeStr The timestamp string
     * @return Seconds since midnight
     */
    private long parseTimestamp(String timeStr) {
        if (timeStr == null || timeStr.isEmpty()) {
            return 0;
        }
        
        String[] parts = timeStr.split(":");
        if (parts.length != 3) {
            return 0;
        }
        
        try {
            int hours = Integer.parseInt(parts[0]);
            int minutes = Integer.parseInt(parts[1]);
            int seconds = Integer.parseInt(parts[2]);
            
            return hours * 3600 + minutes * 60 + seconds;
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    public static void main(String[] args) throws IOException {
        List<SspDevice> devices = SspDeviceCsvImporter.processCsvFile("winmart_camp.csv");
        System.out.println("Processed " + devices.size() + " devices");
        VastHttpClients httpClients = new VastHttpClients();
        String baseUrl = "https://ssp.adtrue.com.vn";

        String logFilePath = "./processed_logs/vast_events_generated.csv";

        for (SspDevice device : devices) {

            DeviceSimulator deviceSimulator = new DeviceSimulator(device.getDevice_id(), baseUrl, httpClients);
            VastReplaySimulator simulator = new VastReplaySimulator(logFilePath, deviceSimulator);
            new Thread(() -> {
                simulator.replay();
            }).start();
//            break;
        }
    }

    /**
     * Print metrics for an HTTP client
     * 
     * @param metrics The metrics to print
     */
    private static void printClientMetrics(HttpClientMetrics metrics) {
        System.out.println("  Total Requests: " + metrics.requests());
        System.out.println("  Successful Responses: " + metrics.successResponses());
        System.out.println("  Error Responses: " + metrics.errorResponses());
        System.out.println("  Success Duration P95 (ms): " + metrics.successDurationP95());
        System.out.println("  Failure Duration P95 (ms): " + metrics.failureDurationP95());
    }
}
