package info.nguyenct.adtrue.simulator;

import info.nguyenct.adtrue.model.VastEventType;
import info.nguyenct.adtrue.restclient.HttpGetClient;
import info.nguyenct.adtrue.restclient.HttpResult;
import info.nguyenct.adtrue.ssp.vast.VastAd;
import info.nguyenct.adtrue.util.Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

@lombok.Getter
public class DeviceSimulator {
    private static final Logger logger = LoggerFactory.getLogger(DeviceSimulator.class);

    private final String deviceId;
    private final String vastUrl;
    private final String macAddress;
    private final String ipAddress;

    // HTTP client for VAST XML requests
    private final VastHttpClients httpClients;
    private VastAd currentVastAd, nextVastAd;
    private List<String> requestedAds = new ArrayList<>();


    public DeviceSimulator(String deviceId, String vastUrl, VastHttpClients httpClients) {
        this.deviceId = deviceId;
        this.vastUrl = vastUrl.toLowerCase();
        this.httpClients = httpClients;

        // Generate random MAC address
        this.macAddress = String.format("%02x:%02x:%02x:%02x:%02x:%02x",
                Util.getRandomInt(256), Util.getRandomInt(256), Util.getRandomInt(256),
                Util.getRandomInt(256), Util.getRandomInt(256), Util.getRandomInt(256));
        this.ipAddress = String.format("192.168.0.%d", Util.getRandomInt(2, 254));
    }

    /**
     * Process a single event
     *
     * @param event The type of event
     * @param eventTimestamp The timestamp of the event
     * @return True if the event was processed successfully, false otherwise
     */
    public void processEvent(VastEventType event, long eventTimestamp) {
        logger.info("Processing event: {} for device: {} at timestamp: {}", event.getEventName(), deviceId, eventTimestamp);

        if (currentVastAd == null && event != VastEventType.VAST_REQUEST) {
            logger.error("Cannot track {} event: No VAST ad available", event.getEventName());
            return;
        }

        try {
            switch (event) {
                case VAST_REQUEST:
                    // Event 0 - Request VAST XML
                    VastAd vastAd = requestVastXml(eventTimestamp);
                    if (currentVastAd == null) {
                        currentVastAd = vastAd;
                    }else {
                        nextVastAd = vastAd;
                    }
                    if (vastAd == null) {
                        requestedAds.add("null ads");
                        return;
                    }else if (!requestedAds.contains(vastAd.getAdId())){
                        requestedAds.add(vastAd.getAdId());
                    }
                    return;
                case IMPRESSION:
                    // Event 1 - Track impression
                    sendTrackingRequest(event.getEventName(), httpClients.impressionClient,
                            currentVastAd.getImpressionUrls(), currentVastAd.getDuration(), eventTimestamp);
                    return;
                case CREATIVE_VIEW:
                    // Event 2 - Track creative view
                    sendTrackingRequest(event.getEventName(), httpClients.creativeViewClient,
                            currentVastAd.getCreativeViewUrls(), currentVastAd.getDuration(), eventTimestamp);
                    return;
                case START:
                    // Event 3 - Track start
                    sendTrackingRequest(event.getEventName(), httpClients.startClient,
                            currentVastAd.getStartUrls(), currentVastAd.getDuration(), eventTimestamp);
                    return;
                case FIRST_QUARTILE:
                    // Event 4 - Track first quartile
                    sendTrackingRequest(event.getEventName(), httpClients.firstQuartileClient,
                            currentVastAd.getFirstQuartileUrls(), currentVastAd.getDuration(), eventTimestamp);
                    return;
                case MIDPOINT:
                    sendTrackingRequest(event.getEventName(), httpClients.midpointClient,
                            currentVastAd.getMidpointUrls(), currentVastAd.getDuration(), eventTimestamp);
                    return;
                case THIRD_QUARTILE:
                    // Event 6 - Track third quartile
                    sendTrackingRequest(event.getEventName(), httpClients.thirdQuartileClient,
                            currentVastAd.getThirdQuartileUrls(), currentVastAd.getDuration(), eventTimestamp);
                    return;
                case COMPLETE:
                    // Event 7 - Track complete
                    sendTrackingRequest(event.getEventName(), httpClients.completeClient,
                            currentVastAd.getCompleteUrls(), currentVastAd.getDuration(), eventTimestamp);
                    currentVastAd = nextVastAd;
                    return;
                default:
                    logger.warn("Unknown event type: {}", event);
            }
        } catch (Exception e) {
            logger.error("Error processing event {}: {}", event.getEventName(), e.getMessage(), e);
        }
    }

    /**
     * Request VAST XML and parse it
     *
     * @param timestamp The timestamp
     * @return True if successful, false otherwise
     */
    private VastAd requestVastXml(long timestamp) {
        String vastUrl = this.vastUrl.replace("[device_id]", deviceId);
        if (timestamp > 0) {
            vastUrl += "&t=" + timestamp;
        }

        if (macAddress != null && !macAddress.isEmpty()) {
            vastUrl += "&mac=" + macAddress;
        }

        logger.info("Requesting VAST XML from: {}", vastUrl);

        HttpResult<VastAd> result = httpClients.vastGetClient.sendGet(vastUrl);

        if (result.isSuccess()) {
            VastAd vastAd = result.value();
            logger.info("Successfully received and parsed VAST XML for ad: {} video {}",
                    vastAd != null ? vastAd.getAdId() : "null", vastAd != null ? vastAd.getFirstMediaFile().getUrl() : "null");
            return vastAd;
        } else {
            logger.error("Failed to get VAST XML, status: {}", result.statusCode());
        }
        return null;
    }

    /**
     * Send a tracking request
     *
     * @param eventName The name of the event
     * @param httpClient The HTTP client to use
     * @param trackingUrls The tracking URLs
     * @param timestamp The timestamp
     * @return True if successful, false otherwise
     */
    private boolean sendTrackingRequest(String eventName, HttpGetClient<String> httpClient, List<String> trackingUrls, int duration, long timestamp) {
        if (trackingUrls == null || trackingUrls.isEmpty()) {
            logger.warn("No tracking URL for {} event", eventName);
            return false;
        }

        for (String trackingUrl : trackingUrls) {
            // Replace placeholders in tracking URL
            String processedUrl = trackingUrl
                    .replace("[duration]", String.valueOf(duration))
                    .replace("[ip]", ipAddress)
                    .replace("[mac]", macAddress)
                    .replace("[timestamp]", String.valueOf(timestamp));

            logger.info("Tracking {} event with URL: {}", eventName, processedUrl);

            HttpResult<String> result = httpClient.sendGet(processedUrl);
        }

        return true;
    }
}
