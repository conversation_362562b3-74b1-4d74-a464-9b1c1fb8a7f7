package info.nguyenct.adtrue.simulator;

import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;

public class GenerateVastEvents {
    private static final String DEVICE_ID = "ATT-OFC-FL2-TEZ-59Q0D2";
    private static final String OUTPUT_FILE = "vast_events_generated.csv";
    private static final int TOTAL_SPOTS = 1200;
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    
    private static final String[] AD_PREFIXES = {"cm_", "lmx_", "modcart_", "vistar_"};
    
    public static void main(String[] args) throws IOException {
        List<EventEntry> events = generateSpots();
        Collections.sort(events, (a, b) -> a.time.compareTo(b.time));
        writeToCSV(events);
        System.out.println("Generated " + TOTAL_SPOTS + " spots (" + events.size() + " events) to " + OUTPUT_FILE);
    }
    
    private static List<EventEntry>  generateSpots() {
        List<EventEntry> events = new ArrayList<>();
        Random random = new Random();
        
        LocalTime startTime = LocalTime.of(8, 0, 0);
        LocalTime endTime = LocalTime.of(22, 0, 0);
        int totalSeconds = (int) java.time.Duration.between(startTime, endTime).getSeconds();
        int intervalSeconds = totalSeconds / TOTAL_SPOTS;
        
        for (int spotIndex = 0; spotIndex < TOTAL_SPOTS; spotIndex++) {
            // Tính thời gian bắt đầu spot
            int baseSeconds = spotIndex * intervalSeconds + random.nextInt(10);
            LocalTime spotStartTime = startTime.plusSeconds(baseSeconds);
            
            // Tạo adId cho spot này
            String adPrefix = AD_PREFIXES[random.nextInt(AD_PREFIXES.length)];
            String adId = adPrefix + String.format("%06d", random.nextInt(1000000));
            long baseTimestamp = 1752022800L + baseSeconds;
            
            // Tạo 8 events cho spot này (0-7)
            LocalTime currentTime = spotStartTime;
            
            // Event 0: VAST_REQUEST
            events.add(new EventEntry(currentTime, DEVICE_ID, 0, adId, baseTimestamp));
            currentTime = currentTime.plusSeconds(random.nextInt(3) + 1); // 1-3s
            
            // Event 2: CREATIVE_VIEW
            events.add(new EventEntry(currentTime, DEVICE_ID, 2, adId, baseTimestamp + random.nextInt(2)));
            currentTime = currentTime.plusSeconds(1);
            
            // Event 3: START
            events.add(new EventEntry(currentTime, DEVICE_ID, 3, adId, baseTimestamp + random.nextInt(2)));
            
            // Event 1: IMPRESSION (sau START)
            events.add(new EventEntry(currentTime, DEVICE_ID, 1, adId, baseTimestamp + random.nextInt(2)));
            currentTime = currentTime.plusSeconds(random.nextInt(4) + 2); // 2-5s

            // 50% khả năng thêm event Event 0: VAST_REQUEST
            if (random.nextBoolean()) {
                events.add(new EventEntry(currentTime, DEVICE_ID, 0, adId, baseTimestamp + random.nextInt(3)));
                currentTime = currentTime.plusSeconds(random.nextInt(2) + 1);
            }
            
            // Event 4: FIRST_QUARTILE
            events.add(new EventEntry(currentTime, DEVICE_ID, 4, adId, baseTimestamp + random.nextInt(5) + 2));
            currentTime = currentTime.plusSeconds(random.nextInt(4) + 2); // 2-5s
            
            // Event 5: MIDPOINT
            events.add(new EventEntry(currentTime, DEVICE_ID, 5, adId, baseTimestamp + random.nextInt(5) + 5));
            currentTime = currentTime.plusSeconds(random.nextInt(4) + 2); // 2-5s
            
            // Event 6: THIRD_QUARTILE
            events.add(new EventEntry(currentTime, DEVICE_ID, 6, adId, baseTimestamp + random.nextInt(5) + 8));
            currentTime = currentTime.plusSeconds(random.nextInt(4) + 2); // 2-5s
            
            // 50% khả năng thêm event Event 0: VAST_REQUEST
            if (random.nextBoolean()) {
                events.add(new EventEntry(currentTime, DEVICE_ID, 0, adId, baseTimestamp + random.nextInt(3)));
                currentTime = currentTime.plusSeconds(random.nextInt(2) + 1);
            }
            
            // Event 7: COMPLETE
            events.add(new EventEntry(currentTime, DEVICE_ID, 7, adId, baseTimestamp + random.nextInt(5) + 12));

            // 50% khả năng thêm event Event 0: VAST_REQUEST
            if (random.nextBoolean()) {
                events.add(new EventEntry(currentTime, DEVICE_ID, 0, adId, baseTimestamp + random.nextInt(3)));
                currentTime = currentTime.plusSeconds(random.nextInt(2) + 1);
            }
        }
        
        return events;
    }
    
    private static void writeToCSV(List<EventEntry> events) throws IOException {
        try (FileWriter writer = new FileWriter(OUTPUT_FILE)) {
            writer.write("timestamp,deviceId,eventType,adId,eventTimestamp\n");
            
            for (EventEntry event : events) {
                writer.write(String.format("%s,%s,%d,%s,%d\n",
                    event.time.format(TIME_FORMATTER),
                    event.deviceId,
                    event.eventType,
                    event.adId,
                    event.eventTimestamp));
            }
        }
    }
    
    static class EventEntry {
        LocalTime time;
        String deviceId;
        int eventType;
        String adId;
        long eventTimestamp;
        
        EventEntry(LocalTime time, String deviceId, int eventType, String adId, long eventTimestamp) {
            this.time = time;
            this.deviceId = deviceId;
            this.eventType = eventType;
            this.adId = adId;
            this.eventTimestamp = eventTimestamp;
        }
    }
}