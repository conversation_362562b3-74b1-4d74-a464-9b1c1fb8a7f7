package info.nguyenct.adtrue.simulator;

import info.nguyenct.adtrue.restclient.HttpGetClient;
import info.nguyenct.adtrue.ssp.vast.VastAd;
import info.nguyenct.adtrue.ssp.vast.VastParser;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.util.function.Function;

/**
 * Manages HTTP clients for VAST replay simulators
 */
public class VastHttpClients {
    HttpGetClient<VastAd> vastGetClient;
    // Shared clients for tracking events
    HttpGetClient<String> impressionClient;
    HttpGetClient<String> creativeViewClient;
    HttpGetClient<String> startClient;
    HttpGetClient<String> firstQuartileClient;
    HttpGetClient<String> midpointClient;
    HttpGetClient<String> thirdQuartileClient;
    HttpGetClient<String> completeClient;

    public VastHttpClients() {
        SimpleMeterRegistry meterRegistry = new SimpleMeterRegistry();
        this.vastGetClient = new HttpGetClient<>("vast-replay-getxml", meterRegistry, new VastParser());
        // Initialize shared tracking clients
        this.impressionClient = new HttpGetClient<>("vast-replay-impression", meterRegistry, Function.identity());
        this.creativeViewClient = new HttpGetClient<>("vast-replay-creativeview", meterRegistry, Function.identity());
        this.startClient = new HttpGetClient<>("vast-replay-start", meterRegistry, Function.identity());
        this.firstQuartileClient = new HttpGetClient<>("vast-replay-firstquartile", meterRegistry, Function.identity());
        this.midpointClient = new HttpGetClient<>("vast-replay-midpoint", meterRegistry, Function.identity());
        this.thirdQuartileClient = new HttpGetClient<>("vast-replay-thirdquartile", meterRegistry, Function.identity());
        this.completeClient = new HttpGetClient<>("vast-replay-complete", meterRegistry, Function.identity());
    }
}