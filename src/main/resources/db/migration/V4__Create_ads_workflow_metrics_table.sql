CREATE TABLE IF NOT EXISTS ads_workflow_metrics (
    id SERIAL PRIMARY KEY,
    collected_at TIMESTAMP NOT NULL,
    ads_requests INT NOT NULL DEFAULT 0,
    cm_ads_responses INT NOT NULL DEFAULT 0,
    vistar_ads_responses INT NOT NULL DEFAULT 0,
    adtrue_ads_responses INT NOT NULL DEFAULT 0,
    cm_cache_size INT NOT NULL DEFAULT 0,
    vistar_cache_size INT NOT NULL DEFAULT 0
);

CREATE INDEX idx_ads_workflow_metrics_collected_at ON ads_workflow_metrics(collected_at);