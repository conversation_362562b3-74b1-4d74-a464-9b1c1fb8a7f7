-- T<PERSON><PERSON> bảng campaign_url_whitelist để lưu trữ whitelist URL cho từng campaign
CREATE TABLE IF NOT EXISTS campaign_url_whitelist (
    id BIGSERIAL PRIMARY KEY,
    campaign_id VARCHAR(255) NOT NULL,
    url VARCHAR(2000) NOT NULL,
    media_path VARCHAR(2000) NOT NULL,
    created_at BIGINT NOT NULL
);

-- Tạo index cho campaign_id để tối ưu query
CREATE INDEX IF NOT EXISTS idx_campaign_url_whitelist_campaign_id ON campaign_url_whitelist(campaign_id);

-- Thêm comment cho bảng
COMMENT ON TABLE campaign_url_whitelist IS 'Bảng lưu trữ whitelist URL cho từng campaign';
COMMENT ON COLUMN campaign_url_whitelist.campaign_id IS 'ID của campaign';
COMMENT ON COLUMN campaign_url_whitelist.url IS 'URL được phép trong whitelist';
COMMENT ON COLUMN campaign_url_whitelist.created_at IS 'Thờ<PERSON> gian <PERSON> (timestamp)';
