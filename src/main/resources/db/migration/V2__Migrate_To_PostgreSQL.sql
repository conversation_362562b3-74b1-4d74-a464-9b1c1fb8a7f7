-- T<PERSON><PERSON> bảng cm_report nếu chưa tồn tại
CREATE TABLE IF NOT EXISTS cm_report (
    id BIGSERIAL PRIMARY KEY,
    device_id VARCHAR(255) NOT NULL,
    banner_id VARCHAR(255) NOT NULL,
    campaign_id VARCHAR(255) NOT NULL,
    channel_id INT NOT NULL DEFAULT 0,
    vendor_id INT NOT NULL DEFAULT 0,
    region_id INT NOT NULL DEFAULT 0,
    city_id INT NOT NULL DEFAULT 0,
    district_id INT NOT NULL DEFAULT 0,
    ward_id INT NOT NULL DEFAULT 0,
    store_id INT NOT NULL DEFAULT 0,
    multiplier INT NOT NULL DEFAULT 1,
    event_date TIMESTAMP NOT NULL,
    total_spot INT DEFAULT 0,
    total_vast_sent INT DEFAULT 0,
    total_start INT DEFAULT 0,
    total_first_quartile INT DEFAULT 0,
    total_midpoint INT DEFAULT 0,
    total_third_quartile INT DEFAULT 0,
    total_complete INT DEFAULT 0,
    total_duration INT DEFAULT 0,
    time TIMESTAMP NOT NULL
);

-- Tạo các index cho cm_report
CREATE INDEX IF NOT EXISTS idx_cm_report_device ON cm_report(device_id);
CREATE INDEX IF NOT EXISTS idx_cm_report_campaign ON cm_report(campaign_id);
CREATE INDEX IF NOT EXISTS idx_cm_report_date ON cm_report(event_date);
CREATE INDEX IF NOT EXISTS idx_cm_report_time ON cm_report(time);
CREATE UNIQUE INDEX IF NOT EXISTS idx_cm_report_dimensions ON cm_report(
    event_date, device_id, banner_id, campaign_id,
    channel_id, vendor_id, region_id, city_id,
    district_id, ward_id, store_id, multiplier);

-- Tạo bảng vast_report nếu chưa tồn tại
CREATE TABLE IF NOT EXISTS vast_report (
    id BIGSERIAL PRIMARY KEY,
    device_id VARCHAR(255) NOT NULL,
    ssp_name VARCHAR(50) NOT NULL,
    campaign_id VARCHAR(255) NOT NULL,
    event_date TIMESTAMP NOT NULL,
    channel_id INT NOT NULL DEFAULT 0,
    vendor_id INT NOT NULL DEFAULT 0,
    region_id INT NOT NULL DEFAULT 0,
    city_id INT NOT NULL DEFAULT 0,
    district_id INT NOT NULL DEFAULT 0,
    ward_id INT NOT NULL DEFAULT 0,
    store_id INT NOT NULL DEFAULT 0,
    multiplier INT NOT NULL DEFAULT 1,
    total_vast_sent INT DEFAULT 0,
    total_start INT DEFAULT 0,
    total_first_quartile INT DEFAULT 0,
    total_midpoint INT DEFAULT 0,
    total_third_quartile INT DEFAULT 0,
    total_complete INT DEFAULT 0,
    total_payout INT DEFAULT 0,
    total_payout_error INT DEFAULT 0,
    total_duration INT DEFAULT 0,
    time TIMESTAMP NOT NULL
);

-- Tạo các index cho vast_report
CREATE INDEX IF NOT EXISTS idx_vast_report_device ON vast_report(device_id);
CREATE INDEX IF NOT EXISTS idx_vast_report_campaign ON vast_report(campaign_id);
CREATE INDEX IF NOT EXISTS idx_vast_report_date ON vast_report(event_date);
CREATE INDEX IF NOT EXISTS idx_vast_report_time ON vast_report(time);
CREATE INDEX IF NOT EXISTS idx_vast_report_location ON vast_report(city_id, district_id, ward_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_vast_report_dimensions ON vast_report(
    event_date, device_id, ssp_name, campaign_id,
    channel_id, vendor_id, region_id, city_id,
    district_id, ward_id, store_id, multiplier
);
