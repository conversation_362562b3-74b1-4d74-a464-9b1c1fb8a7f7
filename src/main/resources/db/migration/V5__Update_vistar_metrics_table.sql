-- C<PERSON><PERSON> nhật bảng vistar_metrics để phù hợp với model VistarMetricRecord

-- <PERSON><PERSON><PERSON> tra và xóa cột total_inventory nếu tồn tại (không có trong model mới)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'vistar_metrics' AND column_name = 'total_inventory') THEN
        ALTER TABLE vistar_metrics DROP COLUMN total_inventory;
    END IF;
END $$;

-- <PERSON><PERSON><PERSON> bảo tất cả các cột cần thiết tồn tại với kiểu dữ liệu đúng
-- Nếu cột đã tồn tại, lệnh này sẽ không làm gì
ALTER TABLE vistar_metrics 
    ADD COLUMN IF NOT EXISTS id BIGSERIAL PRIMARY KEY,
    ADD COLUMN IF NOT EXISTS collected_at TIMESTAMP NOT NULL DEFAULT NOW(),
    ADD COLUMN IF NOT EXISTS ads_requests_sent INT NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS ads_response_received INT NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS ads_response_status_ok INT NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS ads_response_status_non_ok INT NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS ads_slot_requests INT NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS ads_slot_responses INT NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS proof_of_play_calls INT NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS proof_of_play_status_ok INT NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS proof_of_play_status_non_ok INT NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS pending_proof_of_play_calls INT NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS device_count INT NOT NULL DEFAULT 0;

-- Đảm bảo index tồn tại cho trường collected_at
CREATE INDEX IF NOT EXISTS idx_vistar_metrics_collected_at ON vistar_metrics(collected_at);

-- Cập nhật comment cho bảng để ghi chú về cấu trúc mới
COMMENT ON TABLE vistar_metrics IS 'Bảng lưu trữ metrics từ Vistar SSP';

-- Cập nhật bảng ads_workflow_metrics để phù hợp với model AdsWorkflowMetricRecord

-- Kiểm tra và đổi tên cột cm_cache_size và vistar_cache_size thành cache_size nếu cần
DO $$
BEGIN
    -- Kiểm tra xem cột cm_cache_size và vistar_cache_size có tồn tại không
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'ads_workflow_metrics' AND column_name = 'cm_cache_size') AND
       EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'ads_workflow_metrics' AND column_name = 'vistar_cache_size') THEN
        -- Thêm cột mới
        ALTER TABLE ads_workflow_metrics ADD COLUMN cache_size INT NOT NULL DEFAULT 0;

        -- Xóa cột cũ
        ALTER TABLE ads_workflow_metrics DROP COLUMN cm_cache_size;
        ALTER TABLE ads_workflow_metrics DROP COLUMN vistar_cache_size;
    END IF;
END $$;

-- Đảm bảo tất cả các cột cần thiết tồn tại với kiểu dữ liệu đúng
ALTER TABLE ads_workflow_metrics 
    ADD COLUMN IF NOT EXISTS id BIGSERIAL PRIMARY KEY,
    ADD COLUMN IF NOT EXISTS collected_at TIMESTAMP NOT NULL DEFAULT NOW(),
    ADD COLUMN IF NOT EXISTS ads_requests INT NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS cm_ads_responses INT NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS vistar_ads_responses INT NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS adtrue_ads_responses INT NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS cache_size INT NOT NULL DEFAULT 0;

-- Đảm bảo index tồn tại cho trường collected_at
CREATE INDEX IF NOT EXISTS idx_ads_workflow_metrics_collected_at ON ads_workflow_metrics(collected_at);

-- Cập nhật comment cho bảng để ghi chú về cấu trúc mới
COMMENT ON TABLE ads_workflow_metrics IS 'Bảng lưu trữ metrics từ AdsGettingWorkflow';
