CREATE TABLE IF NOT EXISTS ssp_device_metrics (
    id SERIAL PRIMARY KEY,
    device_id VARCHAR(255) NOT NULL,
    channel_id INT NOT NULL,
    vendor_id INT NOT NULL,
    region_id INT NOT NULL,
    city_id INT NOT NULL,
    district_id INT NOT NULL,
    ward_id INT NOT NULL,
    store_id INT NOT NULL,
    multiplier INT NOT NULL,
    in_ads_block_hour INT NOT NULL,
    last_request_time TIMESTAMP NOT NULL,
    collected_at TIMESTAMP NOT NULL
);

CREATE INDEX idx_ssp_device_metrics_device_id ON ssp_device_metrics(device_id);
CREATE INDEX idx_ssp_device_metrics_created_at ON ssp_device_metrics(collected_at);