-- <PERSON><PERSON><PERSON><PERSON> các cột cho event còn thiếu trong vast_report
ALTER TABLE vast_report 
ADD COLUMN IF NOT EXISTS total_impression INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_creative_view INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_click_through INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_error INT DEFAULT 0;

-- Tạo index cho các cột mới
CREATE INDEX IF NOT EXISTS idx_vast_report_impression ON vast_report(total_impression);
CREATE INDEX IF NOT EXISTS idx_vast_report_creative_view ON vast_report(total_creative_view);
CREATE INDEX IF NOT EXISTS idx_vast_report_click_through ON vast_report(total_click_through);
CREATE INDEX IF NOT EXISTS idx_vast_report_error ON vast_report(total_error);

DROP TABLE IF EXISTS temp_vast_report_input;