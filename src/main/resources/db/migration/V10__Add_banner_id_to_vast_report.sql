-- <PERSON><PERSON><PERSON><PERSON> cột banner_id vào bảng vast_report
ALTER TABLE vast_report 
ADD COLUMN IF NOT EXISTS banner_id VARCHAR(255);

-- Tạo index cho banner_id
CREATE INDEX IF NOT EXISTS idx_vast_report_banner ON vast_report(banner_id);

-- C<PERSON><PERSON> nh<PERSON>t unique index để bao gồm banner_id
DROP INDEX IF EXISTS idx_vast_report_dimensions;
CREATE UNIQUE INDEX IF NOT EXISTS idx_vast_report_dimensions ON vast_report(
    event_date, device_id, ssp_name, campaign_id, banner_id,
    channel_id, vendor_id, region_id, city_id,
    district_id, ward_id, store_id, multiplier
);

DROP TABLE "temp_vast_report_input";