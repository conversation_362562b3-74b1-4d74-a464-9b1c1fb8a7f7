CREATE TABLE IF NOT EXISTS vistar_metrics (
    id SERIAL PRIMARY KEY,
    collected_at TIMESTAMP NOT NULL,
    ads_requests_sent INT NOT NULL DEFAULT 0,
    ads_response_received INT NOT NULL DEFAULT 0,
    ads_response_status_ok INT NOT NULL DEFAULT 0,
    ads_response_status_non_ok INT NOT NULL DEFAULT 0,
    ads_slot_requests INT NOT NULL DEFAULT 0,
    ads_slot_responses INT NOT NULL DEFAULT 0,
    proof_of_play_calls INT NOT NULL DEFAULT 0,
    proof_of_play_status_ok INT NOT NULL DEFAULT 0,
    proof_of_play_status_non_ok INT NOT NULL DEFAULT 0,
    pending_proof_of_play_calls INT NOT NULL DEFAULT 0,
    total_inventory INT NOT NULL DEFAULT 0,
    device_count INT NOT NULL DEFAULT 0
);

CREATE INDEX idx_vistar_metrics_collected_at ON vistar_metrics(collected_at);