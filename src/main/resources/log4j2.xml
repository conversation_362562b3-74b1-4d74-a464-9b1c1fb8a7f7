<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Properties>
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</Property>
        <Property name="APP_LOG_ROOT">/app/logs</Property>
        <Property name="RESTCLIENT_CSV_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS},%p,%msg,%X{url},%X{statusCode},%X{body}%n</Property>
        <Property name="RESTCLIENT_CSV_HEADER">timestamp,level,message,url,statusCode,body</Property>
        <Property name="SSP_CSV_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS},%p,%msg,%X{deviceId},%X{adId},%X{url},%X{statusCode},%X{body}%n</Property>
        <Property name="SSP_CSV_HEADER">timestamp,level,message,deviceId,adId,url,statusCode,body</Property>
        
        <!-- Sửa template cho JsonLayout để sử dụng MDC đúng cách -->
        <Property name="REST_CLIENT_JSON_LAYOUT">
            {"timestamp": "%d{yyyy-MM-dd HH:mm:ss.SSS}", "level": "%p", "message": "%m", "url": "%X{url}", "statusCode": "%X{statusCode}", "body": "%X{body}"}
        </Property>
        <Property name="SSP_JSON_LAYOUT">
            {"timestamp": "%d{yyyy-MM-dd HH:mm:ss.SSS}", "level": "%p", "message": "%m", "deviceId": "%X{deviceId}", "adId": "%X{adId}", "url": "%X{url}", "statusCode": "%X{statusCode}", "requestBody": "%X{requestBody}", "body": "%X{body}"}
        </Property>
        <!-- Thêm template mới cho tracking với eventType -->
        <Property name="TRACKING_JSON_LAYOUT">
            {"timestamp": "%d{yyyy-MM-dd HH:mm:ss.SSS}", "level": "%p", "message": "%m", "deviceId": "%X{deviceId}", "adId": "%X{adId}", "eventType": "%X{eventType}", "url": "%X{url}", "statusCode": "%X{statusCode}", "body": "%X{body}"}
        </Property>
    </Properties>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </Console>
        
        <RollingFile name="FileAppender" fileName="${APP_LOG_ROOT}/application.log"
                     filePattern="${APP_LOG_ROOT}/application-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="10MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
        
        <!-- Errors and Warnings Log File -->
        <RollingFile name="ErrorsAppender" fileName="${APP_LOG_ROOT}/errors.log"
                     filePattern="${APP_LOG_ROOT}/errors-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="10MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
        
        <!-- Custom Application Code Log File -->
        <RollingFile name="AppCodeAppender" fileName="${APP_LOG_ROOT}/app-code.log"
                     filePattern="${APP_LOG_ROOT}/app-code-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="10MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- VAST Event CSV Logging -->
        <RollingFile name="VastEventCsvAppender" fileName="${APP_LOG_ROOT}/vast_events.csv"
                     filePattern="${APP_LOG_ROOT}/vast_events-%d{yyyy-MM-dd}-%i.csv">
            <PatternLayout pattern="%d{HH:mm:ss},%X{deviceId},%X{eventType},%X{adId},%X{eventTimestamp},%X{serverTimestamp},%X{logData},%msg%n"
                           header="timestamp,deviceId,eventType,adId,eventTimestamp,serverTimestamp,logData,message%n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- Old VAST Events CSV Logging -->
        <RollingFile name="VastOldEventsCsvAppender" fileName="${APP_LOG_ROOT}/vast_old_events.csv"
                     filePattern="${APP_LOG_ROOT}/vast_old_events-%d{yyyy-MM-dd}-%i.csv">
            <PatternLayout pattern="%X{eventTimestamp},%X{logData}%n"
                           header="timestamp,logData%n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="50MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- SQL Debug Logging -->
        <RollingFile name="SqlDebugAppender" fileName="${APP_LOG_ROOT}/sql_debug.log"
                     filePattern="${APP_LOG_ROOT}/sql_debug-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- SSP Device Fetch JSON Logging -->
        <RollingFile name="SSPDeviceFetchAppender" fileName="${APP_LOG_ROOT}/ssp_device_fetch.json"
                     filePattern="${APP_LOG_ROOT}/ssp_device_fetch-%d{yyyy-MM-dd}-%i.json">
            <PatternLayout pattern="${REST_CLIENT_JSON_LAYOUT}${sys:line.separator}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- CM Get Ad JSON Logging -->
        <RollingFile name="CMGetAdAppender" fileName="${APP_LOG_ROOT}/cm_get_ad.json"
                     filePattern="${APP_LOG_ROOT}/cm_get_ad-%d{yyyy-MM-dd}-%i.json">
            <PatternLayout pattern="${REST_CLIENT_JSON_LAYOUT}${sys:line.separator}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- Vistar Creative Cache JSON Logging -->
        <RollingFile name="VistarCreativeCacheAppender" fileName="${APP_LOG_ROOT}/vistar_creative_cache.json"
                     filePattern="${APP_LOG_ROOT}/vistar_creative_cache-%d{yyyy-MM-dd}-%i.json">
            <PatternLayout pattern="${SSP_JSON_LAYOUT}${sys:line.separator}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- Vistar Ads Request JSON Logging -->
        <RollingFile name="VistarAdsRequestAppender" fileName="${APP_LOG_ROOT}/vistar_ads_request.json"
                     filePattern="${APP_LOG_ROOT}/vistar_ads_request-%d{yyyy-MM-dd}-%i.json">
            <PatternLayout pattern="${SSP_JSON_LAYOUT}${sys:line.separator}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- Vistar Payout JSON Logging -->
        <RollingFile name="VistarPayoutAppender" fileName="${APP_LOG_ROOT}/vistar_ads_payout.json"
                     filePattern="${APP_LOG_ROOT}/vistar_ads_payout-%d{yyyy-MM-dd}-%i.json">
            <PatternLayout pattern="${SSP_JSON_LAYOUT}${sys:line.separator}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- LMX Request JSON Logging -->
        <RollingFile name="LmxRequestAppender" fileName="${APP_LOG_ROOT}/lmx_request.json"
                     filePattern="${APP_LOG_ROOT}/lmx_request-%d{yyyy-MM-dd}-%i.json">
            <PatternLayout pattern="${SSP_JSON_LAYOUT}${sys:line.separator}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- LMX Tracking JSON Logging -->
        <RollingFile name="LmxTrackingAppender" fileName="${APP_LOG_ROOT}/lmx_tracking.json"
                     filePattern="${APP_LOG_ROOT}/lmx_tracking-%d{yyyy-MM-dd}-%i.json">
            <PatternLayout pattern="${TRACKING_JSON_LAYOUT}${sys:line.separator}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- Modcart Request JSON Logging -->
        <RollingFile name="ModcartRequestAppender" fileName="${APP_LOG_ROOT}/modcart_request.json"
                     filePattern="${APP_LOG_ROOT}/modcart_request-%d{yyyy-MM-dd}-%i.json">
            <PatternLayout pattern="${SSP_JSON_LAYOUT}${sys:line.separator}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- Modcart Tracking JSON Appenders -->
        <RollingFile name="ModcartTrackingAppender" fileName="${APP_LOG_ROOT}/modcart_tracking.json"
                     filePattern="${APP_LOG_ROOT}/modcart_tracking-%d{yyyy-MM-dd}-%i.json">
            <PatternLayout pattern="${TRACKING_JSON_LAYOUT}${sys:line.separator}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- LMX Tracking Appenders -->
        <RollingFile name="LmxTrackingAppender" fileName="${APP_LOG_ROOT}/lmx_tracking.json"
                     filePattern="${APP_LOG_ROOT}/lmx_impression_tracking-%d{yyyy-MM-dd}-%i.json">
            <PatternLayout pattern="${TRACKING_JSON_LAYOUT}${sys:line.separator}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
    </Appenders>
    
    <Loggers>
        <Root level="info">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="FileAppender"/>
            <AppenderRef ref="ErrorsAppender" level="WARN"/>
        </Root>
        <!-- Thiết lập level WARN cho Kafka common và consumer để loại bỏ log DEBUG và INFO -->
        <Logger name="org.apache.kafka.common" level="WARN" additivity="false">
            <AppenderRef ref="FileAppender"/>
            <AppenderRef ref="Console"/>
            <AppenderRef ref="ErrorsAppender" level="WARN"/>
        </Logger>
        <Logger name="org.apache.kafka.clients" level="WARN" additivity="false">
            <AppenderRef ref="FileAppender"/>
            <AppenderRef ref="Console"/>
            <AppenderRef ref="ErrorsAppender" level="WARN"/>
        </Logger>
        
        <!-- Cấu hình cho package của ứng dụng -->
        <Logger name="info.nguyenct.adtrue" level="debug" additivity="false">
            <AppenderRef ref="AppCodeAppender"/>
            <AppenderRef ref="Console"/>
            <AppenderRef ref="FileAppender"/>
            <AppenderRef ref="ErrorsAppender" level="WARN"/>
        </Logger>

        <!-- SQL Debug Logger -->
        <Logger name="sql.debug" level="debug" additivity="false">
            <AppenderRef ref="SqlDebugAppender"/>
            <AppenderRef ref="Console" level="debug"/>
        </Logger>

        <!-- CSV logging for video events -->
        <Logger name="vast_events" level="info" additivity="false">
            <AppenderRef ref="VastEventCsvAppender"/>
        </Logger>

        <!-- Old VAST Events CSV Logging -->
        <Logger name="vast_old_events" level="info" additivity="false">
            <AppenderRef ref="VastOldEventsCsvAppender"/>
        </Logger>

        <!-- Structured logging for SSP Device Fetch -->
        <Logger name="ssp-device-fetch" level="info" additivity="false">
            <AppenderRef ref="SSPDeviceFetchAppender"/>
        </Logger>

        <!-- Structured logging for CM get ad -->
        <Logger name="cm-get-ad" level="info" additivity="false">
            <AppenderRef ref="CMGetAdAppender"/>
        </Logger>

        <!-- Structured logging for Vistar creative caching -->
        <Logger name="vistar-creative-cache" level="info" additivity="false">
            <AppenderRef ref="VistarCreativeCacheAppender"/>
        </Logger>

        <!-- Structured logging for Vistar ads request -->
        <Logger name="vistar-get-ad" level="info" additivity="false">
            <AppenderRef ref="VistarAdsRequestAppender"/>
        </Logger>

        <!-- Structured logging for Vistar payout -->
        <Logger name="vistar-payout" level="info" additivity="false">
            <AppenderRef ref="VistarPayoutAppender"/>
        </Logger>

        <!-- Structured logging for LMX request -->
        <Logger name="lmx-get-ad" level="info" additivity="false">
            <AppenderRef ref="LmxRequestAppender"/>
            <AppenderRef ref="Console"/>
        </Logger>

        <!-- LMX Event Tracking Loggers -->
        <Logger name="lmx-tracking" level="info" additivity="false">
            <AppenderRef ref="LmxTrackingAppender"/>
            <AppenderRef ref="Console"/>
        </Logger>

        <!-- Structured logging for Modcart request -->
        <Logger name="modcart-get-ad" level="info" additivity="false">
            <AppenderRef ref="ModcartRequestAppender"/>
            <AppenderRef ref="Console"/>
        </Logger>

        <!-- Modcart Event Tracking Loggers -->
        <Logger name="modcart-tracking" level="info" additivity="false">
            <AppenderRef ref="ModcartTrackingAppender"/>
            <AppenderRef ref="Console"/>
        </Logger>
    </Loggers>
</Configuration>
