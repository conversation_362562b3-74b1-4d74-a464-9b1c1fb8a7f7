package info.nguyenct.adtrue.simulator;

import info.nguyenct.adtrue.model.VastEventType;
import info.nguyenct.adtrue.util.Util;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class DeviceSimulatorIntegrationTest {
    
    private DeviceSimulator simulator;
    private VastHttpClients httpClients;
    private final String deviceId = "WINMP-HNI1F-N-U58QXXQQD";
//    private final String vastUrl = "https://ott.trueview.com.vn/vt_vast/getxml?w=1920&h=1080&cb=[CACHEBUSTER]&app_bundle=[APP_BUNDLE]&banner=1990013&did=[DEVICE_ID]";
    private final String vastUrl = "http://localhost:8080/vt_vast/getxml?w=1920&h=1080&cb=[CACHEBUSTER]&app_bundle=[APP_BUNDLE]&banner=1990013&did=[DEVICE_ID]";
    
    @BeforeEach
    void setUp() {
        httpClients = new VastHttpClients();
        simulator = new DeviceSimulator(deviceId, vastUrl, httpClients);
    }
    
    @Test
    void testFullAdCycle() {
        long timestamp = Util.getTimeStampNow();
        
        // First cycle - Request ad
        simulator.processEvent(VastEventType.VAST_REQUEST, timestamp);
        simulator.processEvent(VastEventType.VAST_REQUEST, timestamp);
        simulator.processEvent(VastEventType.VAST_REQUEST, timestamp);
        assertEquals(1, simulator.getRequestedAds().size(), "Should have requested 1 unique ad");
        
        // First cycle - Play ad
        simulator.processEvent(VastEventType.START, timestamp);
        simulator.processEvent(VastEventType.CREATIVE_VIEW, timestamp);
        simulator.processEvent(VastEventType.IMPRESSION, timestamp);
        simulator.processEvent(VastEventType.FIRST_QUARTILE, timestamp);
        
        // Second cycle - Request ad while first is playing
        simulator.processEvent(VastEventType.VAST_REQUEST, timestamp);
        simulator.processEvent(VastEventType.VAST_REQUEST, timestamp);
        assertEquals(2, simulator.getRequestedAds().size(), "Should have requested 2 unique ads");
        
        // Complete first cycle
        simulator.processEvent(VastEventType.MIDPOINT, timestamp);
        simulator.processEvent(VastEventType.THIRD_QUARTILE, timestamp);
        simulator.processEvent(VastEventType.COMPLETE, timestamp);
        
        // Request more ads but should still have the second ad loaded
        simulator.processEvent(VastEventType.VAST_REQUEST, timestamp);
        simulator.processEvent(VastEventType.VAST_REQUEST, timestamp);
        assertEquals(2, simulator.getRequestedAds().size(), "Should still have 2 unique ads");
        
        // Second cycle - Play ad
        simulator.processEvent(VastEventType.CREATIVE_VIEW, timestamp);
        simulator.processEvent(VastEventType.START, timestamp);
        simulator.processEvent(VastEventType.IMPRESSION, timestamp);
        simulator.processEvent(VastEventType.FIRST_QUARTILE, timestamp);
        simulator.processEvent(VastEventType.MIDPOINT, timestamp);
        simulator.processEvent(VastEventType.THIRD_QUARTILE, timestamp);
        simulator.processEvent(VastEventType.COMPLETE, timestamp);
        
        // Third cycle - Request and play
        simulator.processEvent(VastEventType.VAST_REQUEST, timestamp);
        assertEquals(3, simulator.getRequestedAds().size(), "Should have requested 3 unique ads");
        
        simulator.processEvent(VastEventType.IMPRESSION, timestamp);
        simulator.processEvent(VastEventType.CREATIVE_VIEW, timestamp);
        simulator.processEvent(VastEventType.START, timestamp);
        simulator.processEvent(VastEventType.FIRST_QUARTILE, timestamp);
        simulator.processEvent(VastEventType.MIDPOINT, timestamp);
        simulator.processEvent(VastEventType.THIRD_QUARTILE, timestamp);
        simulator.processEvent(VastEventType.COMPLETE, timestamp);
        
        // Verify final state
        assertEquals(3, simulator.getRequestedAds().size(), "Should have 3 unique ads in total");
        
        // Verify số lần gọi tracking url theo eventType bằng cách kiểm tra HttpClientMetrics

        // Verify VAST requests
        assertEquals(8, httpClients.vastGetClient.getMetrics().requests(),
                "Should have 8 VAST_REQUEST calls");
        
        // Verify tracking events for all 3 ad cycles
        assertEquals(3, httpClients.startClient.getMetrics().requests(),
                "Should have 3 START event calls");
        assertEquals(3, httpClients.creativeViewClient.getMetrics().requests(),
                "Should have 3 CREATIVE_VIEW event calls");
        assertEquals(3, httpClients.impressionClient.getMetrics().requests(),
                "Should have 3 IMPRESSION event calls");
        assertEquals(3, httpClients.firstQuartileClient.getMetrics().requests(),
                "Should have 3 FIRST_QUARTILE event calls");
        assertEquals(3, httpClients.midpointClient.getMetrics().requests(),
                "Should have 3 MIDPOINT event calls");
        assertEquals(3, httpClients.thirdQuartileClient.getMetrics().requests(),
                "Should have 3 THIRD_QUARTILE event calls");
        assertEquals(3, httpClients.completeClient.getMetrics().requests(),
                "Should have 3 COMPLETE event calls");
    }
}