package info.nguyenct.adtrue.ssp.cm;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class CMSspTest {

    @Test
    public void testCalculateSecondsUntilEnd() {
        // Test case with multiple time blocks
        testSecondsUntilEndWithBlocks(
            LocalTime.of(9, 30),  // Current time: 9:30
            Arrays.asList(
                createBlock("08:00", "10:00"),  // Active block: 8:00-10:00
                createBlock("12:00", "14:00"),  // Future block: 12:00-14:00
                createBlock("16:00", "18:00")   // Future block: 16:00-18:00
            ),
            true,
            // Expected: 30min from current block + 2h from 12-14 + 2h from 16-18
            30*60 + 2*60*60 + 2*60*60
        );
        
        // Test case when current time is before all blocks
        testSecondsUntilEndWithBlocks(
            LocalTime.of(7, 0),  // Current time: 7:00
            Arrays.asList(
                createBlock("08:00", "10:00"),  // Future block
                createBlock("12:00", "14:00")   // Future block
            ),
            false,
            // Expected: 2h from 8-10 + 2h from 12-14
            2*60*60 + 2*60*60
        );
        
        // Test case when current time is between blocks
        testSecondsUntilEndWithBlocks(
            LocalTime.of(11, 0),  // Current time: 11:00
            Arrays.asList(
                createBlock("08:00", "10:00"),  // Past block
                createBlock("12:00", "14:00"),  // Future block
                createBlock("16:00", "18:00")   // Future block
            ),
            false,
            // Expected: 2h from 12-14 + 2h from 16-18
            2*60*60 + 2*60*60
        );
        
        // Test case when current time is in the last block
        testSecondsUntilEndWithBlocks(
            LocalTime.of(17, 30),  // Current time: 17:30
            Arrays.asList(
                createBlock("08:00", "10:00"),  // Past block
                createBlock("12:00", "14:00"),  // Past block
                createBlock("16:00", "18:00")   // Active block: 16:00-18:00
            ),
            true,
            // Expected: 30min from current block
            30*60
        );
    }
    
    private void testSecondsUntilEndWithBlocks(LocalTime currentTime, List<CMSpotHourBlock> blocks, boolean expectActive, long expectedSeconds) {
        // Create a campaign with the specified blocks
        CMBanner campaign = new CMBanner();
        campaign.setSpotHourBlock(blocks);
        
        // Calculate seconds until end
        long secondsUntilEnd = 0;
        boolean isActive = false;
        
        for (CMSpotHourBlock block : blocks) {
            LocalTime start = LocalTime.parse(block.getStart());
            LocalTime end = LocalTime.parse(block.getEnd());
            if (currentTime.isBefore(start)) {
                secondsUntilEnd += ChronoUnit.SECONDS.between(start, end);
            } else if (currentTime.isAfter(start) && currentTime.isBefore(end)) {
                isActive = true;
                secondsUntilEnd += ChronoUnit.SECONDS.between(currentTime, end);
            }
        }
        
        assertEquals(expectedSeconds, secondsUntilEnd, "Seconds until end calculation is incorrect");
        assertEquals(expectActive, isActive, "Active status is incorrect");
    }
    
    private CMSpotHourBlock createBlock(String start, String end) {
        CMSpotHourBlock block = new CMSpotHourBlock();
        block.setStart(start);
        block.setEnd(end);
        return block;
    }
}