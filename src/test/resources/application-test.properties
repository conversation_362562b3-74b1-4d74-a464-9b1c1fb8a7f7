# Server Configuration
server.port=8080

# API URLs
vistar.get_ads.url=https://sandbox-api.vistarmedia.com/api/v1/get_ad/json
vistar.network_id=Q_AZDn2gRkWjaQ8KHeNYyw
vistar.api_key=00cdb3a2-5893-4322-9248-8f5b1e7a6d1c

adtrue.api.sspdevice.url=https://adtrue.cloud/api/sspdevice.json
#adtrue.api.sspdevice.url=http://bizfly.local/sspdevice.json
adtrue.api.adspush.url=https://adtrue.cloud/api/sspadspush.json
adtrue.api.key=5YM2HDMG4gnGXKuW

adtrue.api.ssp_id=vistar_test

# Redis Configuration
spring.data.redis.host=localhost
spring.data.redis.port=6379

# Kafka Configuration
spring.kafka.server=localhost:9092
kafka.topic.video_logging_topic=test_video_logging
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer

# MariaDB Configuration
spring.datasource.url=************************************
spring.datasource.username=adtrue
spring.datasource.password=adtrue
spring.datasource.driver-class-name=org.mariadb.jdbc.Driver
